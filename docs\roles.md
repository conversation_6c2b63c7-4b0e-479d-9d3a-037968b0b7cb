# User Roles & Permissions Documentation

## 🎭 Overview

The Bentakon system implements a comprehensive role-based access control (RBAC) system with four distinct user roles, each with specific permissions and capabilities.

## 👥 User Roles

### 1. Admin (إداري)

**Purpose**: System administrators with full control over the platform.

**Capabilities**:
- Complete system management
- User role assignment and management
- Product catalog management
- Financial oversight and reporting
- System configuration and settings

**Access Level**: Full access to all features and data

### 2. Worker (عامل)

**Purpose**: Order processing specialists who handle customer orders.

**Capabilities**:
- View and process all orders
- Accept or decline orders with notes
- Assign digital codes to orders
- Process refunds for declined orders
- View order statistics and reports

**Access Level**: Order management focused, no product creation or user management

### 3. Distributor (موزع)

**Purpose**: Bulk buyers with special pricing privileges.

**Capabilities**:
- Browse products with distributor pricing
- Make purchases at discounted rates
- Access bulk order features
- View purchase history and analytics
- Manage personal account settings

**Access Level**: Enhanced customer with special pricing tier

### 4. User (مستخدم)

**Purpose**: Regular customers who browse and purchase products.

**Capabilities**:
- Browse product catalog
- Make purchases at standard pricing
- Manage wallet and view transaction history
- View order history and status
- Update personal profile information

**Access Level**: Standard customer access

## 🔐 Permission Matrix

| Feature | Admin | Worker | Distributor | User |
|---------|-------|--------|-------------|------|
| **User Management** |
| View all users | ✅ | ❌ | ❌ | ❌ |
| Update user roles | ✅ | ❌ | ❌ | ❌ |
| View user profiles | ✅ | ❌ | ❌ | ❌ |
| **Product Management** |
| Create products | ✅ | ❌ | ❌ | ❌ |
| Edit products | ✅ | ❌ | ❌ | ❌ |
| Delete products | ✅ | ❌ | ❌ | ❌ |
| View products | ✅ | ✅ | ✅ | ✅ |
| Manage digital codes | ✅ | ❌ | ❌ | ❌ |
| **Order Management** |
| View all orders | ✅ | ✅ | ❌ | ❌ |
| Process orders | ✅ | ✅ | ❌ | ❌ |
| Accept/decline orders | ✅ | ✅ | ❌ | ❌ |
| Assign digital codes | ✅ | ✅ | ❌ | ❌ |
| View own orders | ✅ | ✅ | ✅ | ✅ |
| Create orders | ✅ | ✅ | ✅ | ✅ |
| **Financial Operations** |
| View all wallets | ✅ | ❌ | ❌ | ❌ |
| View all transactions | ✅ | ❌ | ❌ | ❌ |
| Process deposits | ✅ | ✅ | ✅ | ✅ |
| View own wallet | ✅ | ✅ | ✅ | ✅ |
| **Pricing Access** |
| Admin pricing | ✅ | ❌ | ❌ | ❌ |
| Distributor pricing | ✅ | ❌ | ✅ | ❌ |
| User pricing | ✅ | ✅ | ✅ | ✅ |
| **System Features** |
| Dashboard analytics | ✅ | ✅ | ❌ | ❌ |
| System settings | ✅ | ❌ | ❌ | ❌ |
| Currency management | ✅ | ❌ | ❌ | ❌ |

## 🏗️ Role Implementation

### Database Level

```sql
-- User role constraint
ALTER TABLE users ADD CONSTRAINT users_role_check 
CHECK (role IN ('admin', 'worker', 'distributor', 'user'));

-- RLS policy example for admin access
CREATE POLICY "Admins can read all users" ON users 
FOR SELECT USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
```

### API Level

```javascript
// Role verification middleware
async function checkUserRole(requiredRoles: string[]) {
  const supabase = await createSupabaseServerClient()
  
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error || !user) {
    throw new Error('غير مصرح')
  }

  const { data: profile } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single()

  if (!profile || !requiredRoles.includes(profile.role)) {
    throw new Error('غير مصرح - صلاحيات غير كافية')
  }

  return { user, profile }
}

// Usage in API routes
export async function POST(request: NextRequest) {
  try {
    // Only admins and workers can access this endpoint
    const { user, profile } = await checkUserRole(['admin', 'worker'])
    
    // Proceed with protected logic
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 403 })
  }
}
```

### Frontend Level

```javascript
// Role-based component rendering
function AdminPanel({ userRole }: { userRole: string }) {
  if (userRole !== 'admin') {
    return <div>غير مصرح بالوصول</div>
  }

  return (
    <div>
      <h2>لوحة الإدارة</h2>
      {/* Admin-only content */}
    </div>
  )
}

// Role-based navigation
function Navigation({ userRole }: { userRole: string }) {
  return (
    <nav>
      <Link href="/products">المنتجات</Link>
      <Link href="/orders">طلباتي</Link>
      
      {['admin', 'worker'].includes(userRole) && (
        <Link href="/admin/orders">إدارة الطلبات</Link>
      )}
      
      {userRole === 'admin' && (
        <>
          <Link href="/admin/products">إدارة المنتجات</Link>
          <Link href="/admin/users">إدارة المستخدمين</Link>
        </>
      )}
    </nav>
  )
}
```

## 💰 Pricing Tiers by Role

### Pricing Structure

```javascript
// Calculate price based on user role
function calculatePrice(product: Product, userRole: string, package?: Package) {
  const pricing = package || product
  
  switch (userRole) {
    case 'admin':
      return pricing.original_price // Cost price for testing
    
    case 'distributor':
      return pricing.distributor_price || pricing.user_price
    
    case 'user':
    case 'worker':
    default:
      return pricing.user_price
  }
}
```

### Pricing Display

| Role | Price Shown | Discount |
|------|-------------|----------|
| **Admin** | Cost price | Testing purposes |
| **Distributor** | Distributor price | 10-20% off user price |
| **User** | Standard price | No discount |
| **Worker** | Standard price | No discount |

## 🔄 Role Assignment Workflow

### 1. Default Role Assignment

```javascript
// New users get 'user' role by default
const { data: user } = await supabase.from('users').insert({
  id: authUser.id,
  email: authUser.email,
  name: userData.name,
  role: 'user', // Default role
  tenant_id: getCurrentTenant()
})
```

### 2. Admin Role Promotion

```javascript
// Only admins can promote users
export async function PATCH(request: NextRequest) {
  // Verify admin role
  const { user, profile } = await checkUserRole(['admin'])
  
  const { userId, newRole } = await request.json()
  
  // Update user role
  const { data: updatedUser } = await supabase
    .from('users')
    .update({ role: newRole })
    .eq('id', userId)
    .eq('tenant_id', getCurrentTenant())
    .single()

  return NextResponse.json({
    success: true,
    data: updatedUser,
    message: 'تم تحديث دور المستخدم بنجاح'
  })
}
```

### 3. Role Verification on Login

```javascript
// Fetch user role on authentication
const { data: profile } = await supabase
  .from('users')
  .select('role, name')
  .eq('id', user.id)
  .single()

// Store role in session/context
setUserRole(profile.role)
```

## 🎨 UI Differences by Role

### Admin Interface

```javascript
// Admin dashboard with full analytics
function AdminDashboard() {
  return (
    <div>
      <StatsCards />
      <UserManagement />
      <ProductManagement />
      <OrderManagement />
      <FinancialReports />
    </div>
  )
}
```

### Worker Interface

```javascript
// Worker interface focused on order processing
function WorkerDashboard() {
  return (
    <div>
      <OrderQueue />
      <OrderProcessing />
      <OrderHistory />
      <WorkerStats />
    </div>
  )
}
```

### Distributor Interface

```javascript
// Distributor interface with special pricing
function DistributorDashboard() {
  return (
    <div>
      <ProductCatalog pricingTier="distributor" />
      <BulkOrderTools />
      <PurchaseHistory />
      <DistributorStats />
    </div>
  )
}
```

### User Interface

```javascript
// Standard customer interface
function UserDashboard() {
  return (
    <div>
      <ProductCatalog pricingTier="user" />
      <OrderHistory />
      <WalletManagement />
      <ProfileSettings />
    </div>
  )
}
```

## 🔒 Security Considerations

### 1. Role Validation

```javascript
// Always validate role server-side
function validateRole(userRole: string, requiredRoles: string[]): boolean {
  return requiredRoles.includes(userRole)
}

// Never trust client-side role information
// Always fetch from database
```

### 2. Privilege Escalation Prevention

```javascript
// Prevent users from changing their own role
CREATE POLICY "Users cannot change own role" ON users 
FOR UPDATE USING (
  auth.uid() != id OR 
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
```

### 3. Role-Based Data Access

```javascript
// Ensure data access respects role boundaries
const query = supabase.from('orders').select('*')

if (userRole === 'admin' || userRole === 'worker') {
  // Can see all orders
  query = query.eq('tenant_id', getCurrentTenant())
} else {
  // Can only see own orders
  query = query.eq('user_id', user.id).eq('tenant_id', getCurrentTenant())
}
```

## 🧪 Testing Role-Based Access

### Test Role Assignment

```javascript
// Test admin role assignment
const response = await fetch('/api/admin/users', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    userId: 'user-uuid',
    role: 'worker'
  })
})

const result = await response.json()
console.log('Role assignment result:', result)
```

### Test Access Control

```javascript
// Test unauthorized access
const response = await fetch('/api/admin/dashboard', {
  headers: {
    'Authorization': `Bearer ${userToken}` // Regular user token
  }
})

// Should return 403 Forbidden
console.log('Access denied:', response.status === 403)
```

## 📊 Role Analytics

### User Distribution

```sql
-- Count users by role
SELECT 
  role,
  COUNT(*) as user_count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM users 
WHERE tenant_id = 'default'
GROUP BY role
ORDER BY user_count DESC;
```

### Role Activity

```sql
-- Track activity by role
SELECT 
  u.role,
  COUNT(o.id) as orders_processed,
  AVG(o.amount) as avg_order_value
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.tenant_id = 'default'
GROUP BY u.role
ORDER BY orders_processed DESC;
```
