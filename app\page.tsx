// Server Component for SSR - Better SEO and Performance
import Link from "next/link"
import { Tag } from "lucide-react"
import { createClient } from '@supabase/supabase-js'
import { TenantResolver } from "./lib/tenant"
import PromoBanner from "./components/PromoBanner"
import HomepageWrapper from "./components/HomepageWrapper"
import type { Product, BannerSlide } from "./types"

// Cache the page for 5 minutes (300 seconds)
export const revalidate = 300

// Secure server-side Supabase client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Server Component - Fetches data at build/request time
export default async function HomePage() {
  console.log('🔄 SSR: Loading homepage data...')

  // Get tenant slug (in production, extract from domain/subdomain)
  const tenantSlug = 'main'

  try {
    // 1. Resolve tenant securely
    const tenant = await TenantResolver.getTenantBySlug(tenantSlug)
    if (!tenant) {
      return (
        <div className="min-h-screen bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-white mb-4">Tenant not found</h1>
            <p className="text-gray-400">The requested tenant could not be found.</p>
          </div>
        </div>
      )
    }

    console.log('✅ SSR: Tenant resolved:', tenant.name)

    // 2. Fetch homepage data (secure server-side)
    const [
      productsResult,
      bannersResult
    ] = await Promise.allSettled([
      // Products - filtered by tenant and active
      supabaseAdmin
        .from('products')
        .select(`
          *,
          packages(*)
        `)
        .eq('tenant_id', tenant.id)
        .eq('active', true)
        .order('created_at', { ascending: false })
        .limit(12), // First 12 for homepage

      // Banners - filtered by tenant and active
      supabaseAdmin
        .from('banner_slides')
        .select('*')
        .eq('tenant_id', tenant.id)
        .eq('active', true)
        .order('order_index', { ascending: true })
    ])

    // Extract data safely
    const products = productsResult.status === 'fulfilled' ? productsResult.value.data || [] : []
    const banners = bannersResult.status === 'fulfilled' ? bannersResult.value.data || [] : []

    console.log('📊 SSR: Data loaded successfully:', {
      products: products.length,
      banners: banners.length
    })

    // Get latest products (since we don't have featured flag in current schema)
    const latestProducts = products.slice(0, 8)

    // Group products by category for display
    const productsByCategory = products.reduce((acc, product) => {
      const category = product.category || 'Other'
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(product)
      return acc
    }, {} as Record<string, Product[]>)

    // Transform banners to match expected format
    const transformedBanners: BannerSlide[] = banners.map(banner => ({
      id: banner.id,
      title: banner.title,
      subtitle: banner.subtitle,
      image: banner.image,
      linkType: banner.link_type as "product" | "collection" | "custom" | "none",
      linkValue: banner.link_value,
      active: banner.active,
      order: banner.order_index
    }))

    return (
      <HomepageWrapper
        initialProducts={products}
        tenant={tenant}
      >
        <div className="min-h-screen bg-gray-900">
          {/* Promotional Banner */}
          <section className="container mx-auto px-4 pt-4 pb-2">
            <PromoBanner initialBanners={transformedBanners} />
          </section>

          {/* Products by Category Section */}
          {Object.keys(productsByCategory).length > 0 && (
            <section className="container mx-auto px-4 py-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
                    🏷️ تصفح حسب الفئة
                  </h2>
                  <p className="text-gray-400">
                    اكتشف منتجاتنا المنظمة حسب الفئات
                  </p>
                </div>
                <Link
                  href="/shop"
                  className="text-purple-400 hover:text-purple-300 transition-colors text-sm font-medium flex items-center space-x-1 space-x-reverse"
                >
                  <span>عرض الكل</span>
                  <Tag className="w-4 h-4" />
                </Link>
              </div>

              {/* Categories Display */}
              <div className="space-y-8">
                {(Object.entries(productsByCategory) as [string, Product[]][]).slice(0, 3).map(([category, categoryProducts]) => (
                  <div key={category}>
                    <h3 className="text-lg font-semibold text-white mb-4">{category}</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {categoryProducts.slice(0, 4).map((product) => (
                        <div key={product.id} className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors">
                          <Link href={`/product/${product.slug}`}>
                            <div className="aspect-square bg-gray-700 rounded-lg mb-3 overflow-hidden">
                              {product.image_url && (
                                <img
                                  src={product.image_url}
                                  alt={product.name}
                                  className="w-full h-full object-cover"
                                />
                              )}
                            </div>
                            <h4 className="font-semibold text-white text-sm mb-1 line-clamp-2">
                              {product.name}
                            </h4>
                            <p className="text-purple-400 font-bold text-sm">
                              ${product.user_price || product.original_price || 'N/A'}
                            </p>
                          </Link>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Latest Products Section */}
          {latestProducts.length > 0 && (
            <section className="container mx-auto px-4 py-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
                    🆕 أحدث المنتجات
                  </h2>
                  <p className="text-gray-400">
                    آخر إضافاتنا من المنتجات الجديدة
                  </p>
                </div>
                <Link
                  href="/shop"
                  className="text-purple-400 hover:text-purple-300 transition-colors text-sm font-medium"
                >
                  عرض الكل
                </Link>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                {latestProducts.map((product) => (
                  <div key={product.id} className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors">
                    <Link href={`/product/${product.slug}`}>
                      <div className="aspect-square bg-gray-700 rounded-lg mb-3 overflow-hidden">
                        {product.image_url && (
                          <img
                            src={product.image_url}
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        )}
                      </div>
                      <h3 className="font-semibold text-white text-sm mb-1 line-clamp-2">
                        {product.name}
                      </h3>
                      <p className="text-purple-400 font-bold text-sm">
                        ${product.user_price || product.original_price || 'N/A'}
                      </p>
                    </Link>
                  </div>
                ))}
              </div>
            </section>
          )}



          {/* All Products Link */}
          {products.length > 0 && (
            <section className="container mx-auto px-4 py-8">
              <div className="text-center">
                <Link
                  href="/shop"
                  className="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-colors"
                >
                  عرض جميع المنتجات
                </Link>
              </div>
            </section>
          )}
        </div>
      </HomepageWrapper>
    )

  } catch (error) {
    console.error('❌ SSR: Error loading homepage:', error)

    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">خطأ في تحميل الصفحة</h1>
          <p className="text-gray-400">حدث خطأ أثناء تحميل بيانات الصفحة الرئيسية</p>
        </div>
      </div>
    )
  }
}
