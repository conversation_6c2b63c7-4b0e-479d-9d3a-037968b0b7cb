"use client"

import { useState, useEffect } from 'react'
import { Plus, Search, Filter, Edit, Trash2, Package, Eye, Star, X } from 'lucide-react'
import ProductForm from './ProductForm/ProductForm'
import type { Product } from '../../types'
import { supabase } from '../../lib/supabase'

interface ProductManagementProps {
  initialProducts?: Product[]
}

export default function ProductManagement({
  initialProducts = []
}: ProductManagementProps) {

  // Ensure initialProducts is always an array
  const safeInitialProducts = Array.isArray(initialProducts) ? initialProducts : []
  const [products, setProducts] = useState<Product[]>(safeInitialProducts)
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(safeInitialProducts)
  const [showForm, setShowForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')



  // Update products when initialProducts changes
  useEffect(() => {
    const safeProducts = Array.isArray(initialProducts) ? initialProducts : []

    if (safeProducts.length > 0) {
      setProducts(safeProducts)
      setFilteredProducts(safeProducts)
    } else {
      // If no initial products, try to load from cache or fetch fresh
      fetchProducts(true)
    }
  }, [initialProducts])

  // Cache invalidation function
  const invalidateProductsCache = () => {
    localStorage.removeItem('bentakon_admin_products')
  }

  // Filter products based on search and filters
  useEffect(() => {
    let filtered = products

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }





    setFilteredProducts(filtered)
  }, [products, searchTerm])

  const fetchProducts = async (useCache: boolean = true) => {
    setLoading(true)
    try {
      // Check cache first if useCache is true
      if (useCache) {
        const cachedProducts = localStorage.getItem('bentakon_admin_products')
        if (cachedProducts) {
          try {
            const { data, timestamp } = JSON.parse(cachedProducts)
            // Use cache if it's less than 5 minutes old
            if (Date.now() - timestamp < 5 * 60 * 1000) {
              console.log('Using cached products data')
              setProducts(data || [])
              setLoading(false)
              return
            }
          } catch (e) {
            console.warn('Failed to parse cached products:', e)
          }
        }
      }

      console.log('Fetching fresh products data')
      const response = await fetch('/api/admin/products', {
        credentials: 'include' // Include cookies for authentication
      })
      if (response.ok) {
        const data = await response.json()
        const products = data.data || []
        setProducts(products)

        // Cache the data
        localStorage.setItem('bentakon_admin_products', JSON.stringify({
          data: products,
          timestamp: Date.now()
        }))
      }
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateProduct = async (formData: any) => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        const result = await response.json()

        // Add new product to cache instead of refetching all data
        const cachedProducts = localStorage.getItem('bentakon_admin_products')
        if (cachedProducts && result.data) {
          try {
            const cache = JSON.parse(cachedProducts)
            if (cache.data) {
              cache.data.unshift(result.data) // Add to beginning of array
              cache.timestamp = Date.now() // Update cache timestamp
              localStorage.setItem('bentakon_admin_products', JSON.stringify(cache))

              // Update local state
              setProducts(prev => [result.data, ...prev])
              console.log('Added new product to cache and local state')
            }
          } catch (e) {
            console.warn('Failed to update cache with new product, will refetch:', e)
            await fetchProducts(false) // Fallback to full refresh
          }
        } else {
          await fetchProducts(false) // Fallback if no cache
        }

        setShowForm(false)
      } else {
        const error = await response.json()
        throw new Error(error.error || 'فشل في إنشاء المنتج')
      }
    } catch (error) {
      console.error('Error creating product:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateProduct = async (formData: any) => {
    if (!editingProduct) return

    setLoading(true)
    try {
      console.log('Updating product:', editingProduct.id)

      // Prepare basic product update payload (custom fields handled separately)
      const updatePayload = {
        name: formData.name,
        slug: formData.slug,
        description: formData.description,
        category: formData.category,
        image_url: formData.image_url,
        active: formData.active,
        original_price: formData.original_price,
        user_price: formData.user_price,
        discount_price: formData.discount_price,
        distributor_price: formData.distributor_price,
        has_packages: formData.has_packages,
        has_codes: formData.has_codes,
        has_custom_fields: formData.has_custom_fields
      }

      console.log('Update payload:', updatePayload)

      // Get authentication token for the request
      const { data: { session } } = await supabase.auth.getSession()
      const authToken = session?.access_token

      console.log('Auth token available:', !!authToken)

      // Single API call to update everything
      const response = await fetch(`/api/admin/products/${editingProduct.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          ...(authToken && { 'Authorization': `Bearer ${authToken}` })
        },
        credentials: 'include',
        body: JSON.stringify(updatePayload)
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        const error = await response.json()
        console.error('Product update failed:', error)
        throw new Error(error.error || 'فشل في تحديث المنتج')
      }

      const result = await response.json()
      let updatedProduct = result.data
      console.log('Basic product data updated successfully')

      // Handle custom fields efficiently
      if (formData.customFields && formData.customFields.length > 0) {
        console.log('Processing custom fields...')
        updatedProduct.custom_fields = formData.customFields
      } else {
        updatedProduct.custom_fields = []
      }

      console.log('Product update completed:', updatedProduct)

      // Update cache with the updated product data
      const cachedProducts = localStorage.getItem('bentakon_admin_products')
      if (cachedProducts) {
        try {
          const cache = JSON.parse(cachedProducts)
          if (cache.data) {
            const productIndex = cache.data.findIndex((p: Product) => p.id === editingProduct.id)
            if (productIndex !== -1) {
              // Replace with updated product data from API response
              cache.data[productIndex] = {
                ...updatedProduct,
                custom_fields: formData.customFields || []
              }
              cache.timestamp = Date.now()
              localStorage.setItem('bentakon_admin_products', JSON.stringify(cache))

              // Update local state
              setProducts(prev => prev.map(p =>
                p.id === editingProduct.id ? cache.data[productIndex] : p
              ))
              console.log('Updated product in cache and local state')
            }
          }
        } catch (e) {
          console.warn('Failed to update cache, will refetch:', e)
          await fetchProducts(false)
        }
      } else {
        // No cache, refetch to get updated data
        await fetchProducts(false)
      }

      setEditingProduct(null)
      setShowForm(false)
    } catch (error) {
      console.error('Error updating product:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteProduct = async (productId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) return

    setLoading(true)
    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        method: 'DELETE',
        credentials: 'include' // Include cookies for authentication
      })

      if (response.ok) {
        // Remove product from cache instead of refetching all data
        const cachedProducts = localStorage.getItem('bentakon_admin_products')
        if (cachedProducts) {
          try {
            const cache = JSON.parse(cachedProducts)
            if (cache.data) {
              cache.data = cache.data.filter((p: Product) => p.id !== productId)
              cache.timestamp = Date.now() // Update cache timestamp
              localStorage.setItem('bentakon_admin_products', JSON.stringify(cache))

              // Update local state
              setProducts(prev => prev.filter(p => p.id !== productId))
              console.log('Removed product from cache and local state')
            }
          } catch (e) {
            console.warn('Failed to update cache after deletion, will refetch:', e)
            await fetchProducts(false) // Fallback to full refresh
          }
        } else {
          await fetchProducts(false) // Fallback if no cache
        }
      } else {
        const error = await response.json()
        alert(error.error || 'فشل في حذف المنتج')
      }
    } catch (error) {
      console.error('Error deleting product:', error)
      alert('حدث خطأ أثناء حذف المنتج')
    } finally {
      setLoading(false)
    }
  }

  const handleEditProduct = (product: Product) => {
    // Simply use the existing product data - no need to fetch additional details
    setEditingProduct(product)
    setShowForm(true)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingProduct(null)
  }

  if (showForm) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4">
        <div className="bg-gray-900 rounded-xl border border-gray-700 w-full max-w-6xl h-full max-h-[95vh] sm:max-h-[90vh] overflow-hidden shadow-2xl">
          <ProductForm
            product={editingProduct || undefined}
            onSubmit={editingProduct ? handleUpdateProduct : handleCreateProduct}
            onCancel={handleCancelForm}
            loading={loading}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">إدارة المنتجات</h1>
          <p className="text-gray-400 mt-1">
            إدارة منتجاتك وحزمها والحقول المخصصة
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowForm(true)}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            إضافة منتج
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="البحث في المنتجات..."
            className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-600 focus:border-purple-500 bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
          />
        </div>





        {/* Results Count */}
        <div className="flex items-center text-sm text-gray-400">
          {filteredProducts.length} من {products.length} منتج
        </div>
      </div>

      {/* Products Grid */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
          <p className="text-gray-400 mt-4">جاري التحميل...</p>
        </div>
      ) : filteredProducts.length === 0 ? (
        <div className="text-center py-12 border-2 border-dashed border-gray-600 rounded-lg">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            {products.length === 0 ? 'لا توجد منتجات' : 'لا توجد نتائج'}
          </h3>
          <p className="text-gray-400 mb-4">
            {products.length === 0 
              ? 'ابدأ بإضافة منتجك الأول'
              : 'جرب تغيير معايير البحث أو الفلترة'
            }
          </p>
          {products.length === 0 && (
            <button
              onClick={() => setShowForm(true)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              إضافة منتج
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className="bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-lg overflow-hidden hover:border-purple-500/50 transition-all"
            >
              {/* Product Image */}
              <div className="relative h-48 bg-gray-800">
                {product.image_url ? (
                  <img
                    src={product.image_url}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Package className="w-16 h-16 text-gray-400" />
                  </div>
                )}

              </div>

              {/* Product Info */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-lg line-clamp-1">{product.name}</h3>
                  <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">
                    {product.category || 'بدون فئة'}
                  </span>
                </div>
                
                <p className="text-gray-400 text-sm line-clamp-2 mb-3">
                  {product.description}
                </p>

                <div className="flex items-center justify-between mb-4">
                  <div className="text-sm">
                    <span className="text-green-400 font-medium">
                      ${product.user_price}
                    </span>
                    {product.discount_price && (
                      <span className="text-gray-400 line-through mr-2">
                        ${product.discount_price}
                      </span>
                    )}
                  </div>
                  <div className="text-xs text-gray-400">
                    منتج
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleEditProduct(product)}
                    disabled={loading}
                    className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm"
                  >
                    <Edit className="w-3 h-3" />
                    تعديل
                  </button>
                  <button
                    onClick={() => handleDeleteProduct(product.id)}
                    disabled={loading}
                    className="px-3 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
