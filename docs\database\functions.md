# Database Functions Documentation

## 🔧 Overview

The Bentakon system uses PostgreSQL functions to handle critical business operations, particularly financial transactions. These functions ensure data consistency, security, and proper error handling.

## 💰 Wallet Management Functions

### 1. `create_user_wallet(user_uuid, tenant_uuid)`

**Purpose**: Creates a new wallet for a user during registration.

```sql
CREATE OR REPLACE FUNCTION create_user_wallet(
    user_uuid UUID, 
    tenant_uuid TEXT DEFAULT 'default'
)
RETURNS UUID AS $$
DECLARE
    wallet_id UUID;
BEGIN
    INSERT INTO wallets (tenant_id, user_id, balance)
    VALUES (tenant_uuid, user_uuid, 0)
    RETURNING id INTO wallet_id;
    
    RETURN wallet_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

**Usage**:
```javascript
// In registration API
const { data: walletId, error } = await supabase
  .rpc('create_user_wallet', {
    user_uuid: user.id,
    tenant_uuid: 'default'
  })
```

**Security**: `SECURITY DEFINER` allows function to run with elevated privileges.

### 2. `calculate_wallet_balance(wallet_uuid)`

**Purpose**: Calculates current wallet balance from transaction history.

```sql
CREATE OR REPLACE FUNCTION calculate_wallet_balance(wallet_uuid UUID)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    total_balance DECIMAL(10,2) := 0;
BEGIN
    SELECT COALESCE(SUM(
        CASE 
            WHEN type = 'deposit' THEN amount
            WHEN type = 'refund' THEN amount
            WHEN type = 'purchase' THEN -amount
            ELSE 0
        END
    ), 0) INTO total_balance
    FROM transactions 
    WHERE wallet_id = wallet_uuid AND status = 'confirmed';
    
    RETURN total_balance;
END;
$$ LANGUAGE plpgsql;
```

**Business Logic**:
- `deposit` and `refund`: Add to balance
- `purchase`: Subtract from balance
- Only `confirmed` transactions count
- Returns 0 if no transactions exist

### 3. `process_deposit(user_uuid, amount_val, currency_code_val, exchange_rate_val, description_val)`

**Purpose**: Processes wallet deposits with currency conversion.

```sql
CREATE OR REPLACE FUNCTION process_deposit(
    user_uuid UUID,
    amount_val DECIMAL(10,2),
    currency_code_val TEXT DEFAULT 'USD',
    exchange_rate_val DECIMAL(10,6) DEFAULT 1.0,
    description_val TEXT DEFAULT 'Wallet deposit'
)
RETURNS UUID AS $$
DECLARE
    wallet_id UUID;
    transaction_id UUID;
    usd_amount DECIMAL(10,2);
BEGIN
    -- Get or create wallet
    SELECT id INTO wallet_id FROM wallets WHERE user_id = user_uuid;
    IF wallet_id IS NULL THEN
        wallet_id := create_user_wallet(user_uuid);
    END IF;
    
    -- Convert to USD
    usd_amount := amount_val * exchange_rate_val;
    
    -- Create transaction
    INSERT INTO transactions (user_id, wallet_id, type, amount, currency_code, exchange_rate, description)
    VALUES (user_uuid, wallet_id, 'deposit', usd_amount, currency_code_val, exchange_rate_val, description_val)
    RETURNING id INTO transaction_id;
    
    -- Update wallet balance
    UPDATE wallets 
    SET balance = calculate_wallet_balance(wallet_id), updated_at = NOW()
    WHERE id = wallet_id;
    
    RETURN transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

**Features**:
- Automatic wallet creation if doesn't exist
- Currency conversion to USD
- Atomic transaction creation and balance update
- Returns transaction ID for reference

**Usage Example**:
```javascript
// Deposit 100 EUR (exchange rate 0.85)
const { data: transactionId, error } = await supabase
  .rpc('process_deposit', {
    user_uuid: user.id,
    amount_val: 100,
    currency_code_val: 'EUR',
    exchange_rate_val: 0.85,
    description_val: 'Deposit via credit card'
  })
```

### 4. `process_purchase(user_uuid, amount_val, order_uuid, description_val)`

**Purpose**: Deducts money from wallet for purchases with balance validation.

```sql
CREATE OR REPLACE FUNCTION process_purchase(
    user_uuid UUID,
    amount_val DECIMAL(10,2),
    order_uuid UUID,
    description_val TEXT DEFAULT 'Purchase'
)
RETURNS UUID AS $$
DECLARE
    wallet_id UUID;
    transaction_id UUID;
    current_balance DECIMAL(10,2);
BEGIN
    -- Get wallet
    SELECT id, balance INTO wallet_id, current_balance FROM wallets WHERE user_id = user_uuid;
    IF wallet_id IS NULL THEN
        RAISE EXCEPTION 'Wallet not found for user';
    END IF;
    
    -- Check sufficient balance
    IF current_balance < amount_val THEN
        RAISE EXCEPTION 'Insufficient wallet balance';
    END IF;
    
    -- Create transaction
    INSERT INTO transactions (user_id, wallet_id, type, amount, reference_id, description)
    VALUES (user_uuid, wallet_id, 'purchase', amount_val, order_uuid, description_val)
    RETURNING id INTO transaction_id;
    
    -- Update wallet balance
    UPDATE wallets 
    SET balance = calculate_wallet_balance(wallet_id), updated_at = NOW()
    WHERE id = wallet_id;
    
    RETURN transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

**Validation**:
- Ensures wallet exists
- Validates sufficient balance
- Raises exception if insufficient funds
- Links transaction to order via `reference_id`

**Error Handling**:
```javascript
try {
  const { data: transactionId, error } = await supabase
    .rpc('process_purchase', {
      user_uuid: user.id,
      amount_val: 50.00,
      order_uuid: orderId,
      description_val: 'Purchase: Gaming Package'
    })
} catch (error) {
  // Handle insufficient balance or other errors
  console.error('Purchase failed:', error.message)
}
```

### 5. `process_refund(user_uuid, amount_val, order_uuid, description_val)`

**Purpose**: Processes refunds for declined orders.

```sql
CREATE OR REPLACE FUNCTION process_refund(
    user_uuid UUID,
    amount_val DECIMAL(10,2),
    order_uuid UUID,
    description_val TEXT DEFAULT 'Order refund'
)
RETURNS UUID AS $$
DECLARE
    wallet_id UUID;
    transaction_id UUID;
BEGIN
    -- Get wallet
    SELECT id INTO wallet_id FROM wallets WHERE user_id = user_uuid;
    IF wallet_id IS NULL THEN
        wallet_id := create_user_wallet(user_uuid);
    END IF;
    
    -- Create refund transaction
    INSERT INTO transactions (user_id, wallet_id, type, amount, reference_id, description)
    VALUES (user_uuid, wallet_id, 'refund', amount_val, order_uuid, description_val)
    RETURNING id INTO transaction_id;
    
    -- Update wallet balance
    UPDATE wallets 
    SET balance = calculate_wallet_balance(wallet_id), updated_at = NOW()
    WHERE id = wallet_id;
    
    RETURN transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

**Features**:
- Creates wallet if doesn't exist (edge case handling)
- Links refund to original order
- Immediately updates wallet balance
- Returns transaction ID for audit trail

## 🔄 Function Usage Patterns

### **Registration Flow**
```javascript
// 1. User registers via Supabase Auth
const { data: authUser } = await supabase.auth.signUp({email, password})

// 2. Create user profile
await supabase.from('users').insert({
  id: authUser.user.id,
  email, name, role: 'user'
})

// 3. Create wallet automatically
await supabase.rpc('create_user_wallet', {
  user_uuid: authUser.user.id
})
```

### **Purchase Flow**
```javascript
// 1. Calculate pricing
const finalPrice = calculatePrice(product, userRole, pricingTier)

// 2. Deduct from wallet
const { data: transactionId } = await supabase
  .rpc('process_purchase', {
    user_uuid: user.id,
    amount_val: finalPrice,
    order_uuid: null, // Will update after order creation
    description_val: `Purchase: ${product.name}`
  })

// 3. Create order
const { data: order } = await supabase
  .from('orders')
  .insert({...orderData})

// 4. Link transaction to order
await supabase
  .from('transactions')
  .update({reference_id: order.id})
  .eq('id', transactionId)
```

### **Refund Flow**
```javascript
// When worker declines order
if (newStatus === 'declined') {
  // Process automatic refund
  await supabase.rpc('process_refund', {
    user_uuid: order.user_id,
    amount_val: order.amount,
    order_uuid: order.id,
    description_val: `Refund for declined order: ${declineReason}`
  })
}
```

## ⚠️ Security Considerations

### **Function Security**
- All financial functions use `SECURITY DEFINER`
- Functions run with database owner privileges
- Input validation prevents SQL injection
- Exception handling prevents partial operations

### **Error Handling**
- Functions raise exceptions for invalid operations
- Transactions are atomic (all-or-nothing)
- Balance calculations are always consistent
- Audit trail is maintained for all operations

### **Performance Optimization**
- Functions minimize database round trips
- Balance calculations are cached in wallets table
- Indexes support efficient function operations
- Functions handle edge cases (missing wallets, etc.)

## 🧪 Testing Functions

### **Test Deposit**
```sql
-- Test deposit function
SELECT process_deposit(
  'user-uuid-here'::UUID,
  100.00,
  'USD',
  1.0,
  'Test deposit'
);

-- Verify balance
SELECT balance FROM wallets WHERE user_id = 'user-uuid-here';
```

### **Test Purchase**
```sql
-- Test purchase (should fail if insufficient balance)
SELECT process_purchase(
  'user-uuid-here'::UUID,
  150.00,
  'order-uuid-here'::UUID,
  'Test purchase'
);
```

### **Test Balance Calculation**
```sql
-- Verify balance calculation
SELECT 
  w.balance as stored_balance,
  calculate_wallet_balance(w.id) as calculated_balance
FROM wallets w 
WHERE user_id = 'user-uuid-here';
```

## 🔧 Function Maintenance

### **Updating Functions**
1. Always use `CREATE OR REPLACE FUNCTION`
2. Test in development environment first
3. Backup database before production updates
4. Monitor function performance after updates

### **Adding New Functions**
1. Follow naming convention: `process_*` for operations
2. Include proper error handling
3. Use `SECURITY DEFINER` for privileged operations
4. Document parameters and return values
5. Add comprehensive tests
