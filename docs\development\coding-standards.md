# Coding Standards & Best Practices

## 🎯 Overview

This document establishes coding standards and best practices for the Bentakon e-commerce system to ensure consistency, maintainability, and security across the codebase.

## 📁 File Organization

### Directory Structure

```
bentakon-store/
├── app/                    # Next.js App Router
│   ├── api/               # API routes (server-side only)
│   ├── components/        # Reusable UI components
│   ├── (auth)/           # Auth-related pages
│   ├── admin/            # Admin-only pages
│   ├── types/            # TypeScript type definitions
│   └── globals.css       # Global styles
├── components/ui/         # Shadcn/UI components
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions and configurations
├── docs/                 # Documentation
└── public/              # Static assets
```

### File Naming Conventions

```bash
# Components (PascalCase)
ProductCard.tsx
UserDashboard.tsx
OrderProcessing.tsx

# Pages (kebab-case)
product-details/page.tsx
user-profile/page.tsx
admin-dashboard/page.tsx

# API routes (kebab-case)
api/auth/login/route.ts
api/wallet/deposit/route.ts
api/admin/users/route.ts

# Utilities (camelCase)
serverUtils.ts
validationSchemas.ts
databaseHelpers.ts

# Types (camelCase with .types suffix)
database.types.ts
api.types.ts
user.types.ts
```

## 🔧 TypeScript Standards

### Type Definitions

```typescript
// Use interfaces for object shapes
interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'worker' | 'distributor' | 'user'
  created_at: string
}

// Use types for unions and computed types
type UserRole = User['role']
type CreateUserData = Omit<User, 'id' | 'created_at'>

// Use enums for constants
enum OrderStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  COMPLETED = 'completed'
}
```

### API Response Types

```typescript
// Standardized API response structure
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  details?: any[]
}

// Usage in API routes
export async function GET(): Promise<NextResponse<ApiResponse<User[]>>> {
  try {
    const users = await getUsers()
    return NextResponse.json({
      success: true,
      data: users
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch users'
    }, { status: 500 })
  }
}
```

### Strict Type Checking

```typescript
// Enable strict mode in tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}

// Use type assertions carefully
const user = data as User // Only when you're certain
const user = data satisfies User // Preferred for validation
```

## ⚛️ React Component Standards

### Component Structure

```typescript
// Component template
interface ComponentProps {
  // Props interface first
  title: string
  onAction?: () => void
  children?: React.ReactNode
}

export default function Component({ title, onAction, children }: ComponentProps) {
  // Hooks at the top
  const [state, setState] = useState<string>('')
  const { data, error } = useSWR('/api/data')
  
  // Event handlers
  const handleClick = useCallback(() => {
    onAction?.()
  }, [onAction])
  
  // Early returns for loading/error states
  if (error) return <ErrorComponent error={error} />
  if (!data) return <LoadingSpinner />
  
  // Main render
  return (
    <div className="component-container">
      <h2>{title}</h2>
      {children}
      <button onClick={handleClick}>Action</button>
    </div>
  )
}
```

### Server vs Client Components

```typescript
// Server Component (default in App Router)
// - No useState, useEffect, or browser APIs
// - Can directly access database
// - Better for SEO and performance
export default async function ServerComponent() {
  const data = await fetchDataDirectly()
  
  return (
    <div>
      <h1>Server Rendered Content</h1>
      <ClientComponent data={data} />
    </div>
  )
}

// Client Component (use 'use client' directive)
// - Can use hooks and browser APIs
// - Interactive functionality
// - State management
'use client'

export default function ClientComponent({ data }: { data: any }) {
  const [isOpen, setIsOpen] = useState(false)
  
  return (
    <div>
      <button onClick={() => setIsOpen(!isOpen)}>
        Toggle
      </button>
      {isOpen && <div>{data.content}</div>}
    </div>
  )
}
```

### Custom Hooks

```typescript
// Custom hook naming: use + PascalCase
export function useWallet() {
  const [balance, setBalance] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const fetchBalance = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/wallet')
      const data = await response.json()
      
      if (data.success) {
        setBalance(data.data.wallet.balance)
      } else {
        setError(data.error)
      }
    } catch (err) {
      setError('Failed to fetch wallet balance')
    } finally {
      setLoading(false)
    }
  }, [])
  
  useEffect(() => {
    fetchBalance()
  }, [fetchBalance])
  
  return { balance, loading, error, refetch: fetchBalance }
}
```

## 🔒 Security Standards

### API Route Security

```typescript
// Always validate authentication
export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClient()
  
  // Check authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    return NextResponse.json(
      { success: false, error: 'غير مصرح' },
      { status: 401 }
    )
  }
  
  // Validate user role if needed
  const { data: profile } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single()
  
  if (!profile || !['admin', 'worker'].includes(profile.role)) {
    return NextResponse.json(
      { success: false, error: 'غير مصرح - صلاحيات غير كافية' },
      { status: 403 }
    )
  }
  
  // Continue with protected logic
}
```

### Input Validation

```typescript
import { z } from 'zod'

// Define validation schemas
const createUserSchema = z.object({
  name: z.string().min(2).max(50),
  email: z.string().email(),
  password: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
  role: z.enum(['admin', 'worker', 'distributor', 'user'])
})

// Validate in API routes
export async function POST(request: NextRequest) {
  const body = await request.json()
  
  const validationResult = createUserSchema.safeParse(body)
  if (!validationResult.success) {
    return NextResponse.json({
      success: false,
      error: 'بيانات غير صالحة',
      details: validationResult.error.errors
    }, { status: 400 })
  }
  
  const validatedData = validationResult.data
  // Use validatedData safely
}
```

### Database Security

```typescript
// Always use parameterized queries
const { data, error } = await supabase
  .from('orders')
  .select('*')
  .eq('user_id', user.id) // Safe parameter
  .eq('tenant_id', getCurrentTenant()) // Tenant isolation

// Never use string concatenation
// BAD: .select(`* WHERE id = '${userId}'`)
// GOOD: .eq('id', userId)
```

## 🎨 Styling Standards

### Tailwind CSS Guidelines

```typescript
// Use consistent spacing scale
const spacing = {
  xs: 'p-2',    // 8px
  sm: 'p-4',    // 16px
  md: 'p-6',    // 24px
  lg: 'p-8',    // 32px
  xl: 'p-12'    // 48px
}

// Component with consistent styling
export default function Card({ children, variant = 'default' }: CardProps) {
  const baseClasses = 'rounded-lg border shadow-sm'
  const variantClasses = {
    default: 'bg-white border-gray-200',
    primary: 'bg-blue-50 border-blue-200',
    danger: 'bg-red-50 border-red-200'
  }
  
  return (
    <div className={`${baseClasses} ${variantClasses[variant]} p-6`}>
      {children}
    </div>
  )
}
```

### CSS Custom Properties

```css
/* globals.css - Define consistent design tokens */
:root {
  /* Colors */
  --color-primary: #3b82f6;
  --color-primary-dark: #1d4ed8;
  --color-success: #10b981;
  --color-danger: #ef4444;
  --color-warning: #f59e0b;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  
  /* Typography */
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
}
```

## 🔄 State Management

### Local State

```typescript
// Use useState for component-local state
function ProductForm() {
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: 0
  })
  
  const updateField = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }
  
  return (
    <form>
      <input 
        value={formData.name}
        onChange={(e) => updateField('name', e.target.value)}
      />
    </form>
  )
}
```

### Server State

```typescript
// Use SWR for server state management
function UserDashboard() {
  const { data: user, error, mutate } = useSWR('/api/auth/user', fetcher)
  const { data: wallet } = useSWR('/api/wallet', fetcher)
  
  if (error) return <ErrorMessage error={error} />
  if (!user) return <LoadingSpinner />
  
  return (
    <div>
      <UserProfile user={user} />
      <WalletBalance wallet={wallet} onUpdate={() => mutate()} />
    </div>
  )
}
```

## 🧪 Testing Standards

### Unit Tests

```typescript
// Component testing with React Testing Library
import { render, screen, fireEvent } from '@testing-library/react'
import { ProductCard } from './ProductCard'

describe('ProductCard', () => {
  const mockProduct = {
    id: '1',
    name: 'Test Product',
    price: 25.00,
    image: 'test.jpg'
  }
  
  it('displays product information correctly', () => {
    render(<ProductCard product={mockProduct} />)
    
    expect(screen.getByText('Test Product')).toBeInTheDocument()
    expect(screen.getByText('$25.00')).toBeInTheDocument()
  })
  
  it('calls onPurchase when buy button is clicked', () => {
    const mockOnPurchase = jest.fn()
    render(<ProductCard product={mockProduct} onPurchase={mockOnPurchase} />)
    
    fireEvent.click(screen.getByText('شراء'))
    expect(mockOnPurchase).toHaveBeenCalledWith(mockProduct.id)
  })
})
```

### API Tests

```typescript
// API route testing
import { createMocks } from 'node-mocks-http'
import handler from '../api/products/route'

describe('/api/products', () => {
  it('returns products for authenticated user', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      headers: {
        authorization: 'Bearer valid-token'
      }
    })
    
    await handler(req, res)
    
    expect(res._getStatusCode()).toBe(200)
    const data = JSON.parse(res._getData())
    expect(data.success).toBe(true)
    expect(Array.isArray(data.data)).toBe(true)
  })
})
```

## 📝 Documentation Standards

### Code Comments

```typescript
/**
 * Calculates the final price for an order based on user role and pricing tier
 * 
 * @param product - The product being purchased
 * @param package - Optional package variant
 * @param userRole - User's role (affects pricing tier access)
 * @param pricingTier - Requested pricing tier
 * @returns Object containing final price, original price, and profit
 * @throws Error if user doesn't have access to requested pricing tier
 */
function calculateOrderPrice(
  product: Product,
  package: Package | null,
  userRole: UserRole,
  pricingTier: PricingTier
): PriceCalculation {
  // Implementation details...
}
```

### README Files

```markdown
# Component Name

Brief description of what this component does.

## Usage

```tsx
import { ComponentName } from './ComponentName'

<ComponentName 
  prop1="value1"
  prop2={value2}
  onAction={handleAction}
/>
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| prop1 | string | Yes | Description of prop1 |
| prop2 | number | No | Description of prop2 |

## Examples

[Include usage examples]
```

## 🔧 Performance Standards

### Code Splitting

```typescript
// Lazy load heavy components
import { lazy, Suspense } from 'react'

const AdminDashboard = lazy(() => import('./AdminDashboard'))
const ReportsPanel = lazy(() => import('./ReportsPanel'))

export default function AdminPage() {
  return (
    <div>
      <Suspense fallback={<LoadingSpinner />}>
        <AdminDashboard />
        <ReportsPanel />
      </Suspense>
    </div>
  )
}
```

### Memoization

```typescript
// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return calculateComplexValue(data)
}, [data])

// Memoize callback functions
const handleClick = useCallback((id: string) => {
  onItemClick(id)
}, [onItemClick])

// Memoize components
const MemoizedComponent = memo(function Component({ data }: Props) {
  return <div>{data.content}</div>
})
```

## 🚀 Deployment Standards

### Environment Configuration

```typescript
// Use environment variables for configuration
const config = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY!
  },
  app: {
    url: process.env.NEXT_PUBLIC_APP_URL!,
    tenantId: process.env.TENANT_ID || 'default'
  }
}

// Validate required environment variables
function validateEnv() {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
  
  for (const key of required) {
    if (!process.env[key]) {
      throw new Error(`Missing required environment variable: ${key}`)
    }
  }
}
```

### Build Optimization

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['@supabase/supabase-js']
  },
  
  // Optimize images
  images: {
    domains: ['your-cdn-domain.com'],
    formats: ['image/webp', 'image/avif']
  },
  
  // Bundle analyzer
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      }
    }
    return config
  }
}

module.exports = nextConfig
```

---

**Code Quality**: Following these standards ensures consistent, maintainable, and secure code across the entire Bentakon platform.
