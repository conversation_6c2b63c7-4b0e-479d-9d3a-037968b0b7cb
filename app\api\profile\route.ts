import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../lib/supabase/server'
import { z } from 'zod'

// Validation schema for profile updates
const updateProfileSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  phone: z.string().optional(),
  avatar: z.string().url().optional()
})

// GET /api/profile - Get current user's profile
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user profile with wallet balance
    const { data: profile, error } = await supabase
      .from('users')
      .select(`
        *,
        wallets (
          balance
        )
      `)
      .eq('id', user.id)
      .single()

    if (error) {
      console.error('Error fetching profile:', error)
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 })
    }

    return NextResponse.json({
      user: {
        id: profile.id,
        email: user.email,
        name: profile.name,
        role: profile.role,
        phone: profile.phone,
        avatar: profile.avatar,
        walletBalance: profile.wallets?.[0]?.balance || 0,
        tenantId: profile.tenant_id,
        createdAt: profile.created_at
      }
    })

  } catch (error) {
    console.error('Error in profile GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/profile - Update current user's profile
export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateProfileSchema.parse(body)

    // Update user profile
    const { data: updatedProfile, error } = await supabase
      .from('users')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating profile:', error)
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      user: {
        id: updatedProfile.id,
        name: updatedProfile.name,
        phone: updatedProfile.phone,
        avatar: updatedProfile.avatar,
        updatedAt: updatedProfile.updated_at
      }
    })

  } catch (error) {
    console.error('Error in profile PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
