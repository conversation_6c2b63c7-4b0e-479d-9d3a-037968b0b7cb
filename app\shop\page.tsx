"use client"

import { useState, useEffect } from 'react'
import { Package, Heart, ShoppingCart } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import type { Product } from '../types'

interface ProductWithCategory extends Product {
  packages_count?: number
}

export default function ShopPage() {
  const [products, setProducts] = useState<ProductWithCategory[]>([])
  const [loading, setLoading] = useState(true)


  // Fetch products
  useEffect(() => {
    const fetchData = async () => {
      try {
        const productsRes = await fetch('/api/admin/products', { credentials: 'include' })

        if (productsRes.ok) {
          const productsData = await productsRes.json()
          console.log('Shop products loaded:', productsData.data)
          setProducts(productsData.data || [])
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Use all products since we don't have category filtering anymore
  const filteredProducts = products

  // Get effective price for display
  const getEffectivePrice = (product: ProductWithCategory) => {
    if (product.discount_price) return product.discount_price
    return product.user_price || 0
  }

  // Get original price for comparison
  const getOriginalPrice = (product: ProductWithCategory) => {
    return product.user_price || 0
  }

  // Check if product has discount
  const hasDiscount = (product: ProductWithCategory) => {
    return product.discount_price && product.user_price && product.discount_price < product.user_price
  }

  // Calculate discount percentage
  const getDiscountPercentage = (product: ProductWithCategory) => {
    if (!hasDiscount(product)) return 0
    const original = product.user_price!
    const discounted = product.discount_price!
    return Math.round(((original - discounted) / original) * 100)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4 py-8">


        {/* Products Grid */}
        {filteredProducts.length === 0 ? (
          <div className="text-center py-16">
            <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-300 mb-2">لا توجد منتجات</h3>
            <p className="text-gray-500">لم يتم العثور على منتجات تطابق البحث</p>
          </div>
        ) : (
          <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 sm:gap-4">
            {filteredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Product Card Component
function ProductCard({ product }: { product: ProductWithCategory }) {
  const effectivePrice = getEffectivePrice(product)
  const originalPrice = getOriginalPrice(product)
  const discount = hasDiscount(product)
  const discountPercentage = getDiscountPercentage(product)

  return (
    <Link href={`/product/${product.slug}`}>
      <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-lg overflow-hidden hover:border-purple-500/50 transition-all duration-300 hover:scale-[1.02] group h-full flex flex-col">
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden">
          {product.image_url ? (
            <Image
              src={product.image_url}
              alt={product.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 640px) 33vw, (max-width: 768px) 25vw, (max-width: 1024px) 20vw, 16vw"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.src = "/logo.jpg"
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-800">
              <Package className="w-8 h-8 text-gray-400" />
            </div>
          )}

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {discount && (
              <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded text-xs font-bold">
                -{discountPercentage}%
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button className="bg-black/50 hover:bg-black/70 text-white p-1.5 rounded-full transition-colors">
              <Heart className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Product Info */}
        <div className="p-3 flex-1 flex flex-col">
          {/* Title */}
          <h3 className="text-sm font-semibold text-white mb-2 line-clamp-2 leading-tight">
            {product.name}
          </h3>

          {/* Price */}
          <div className="mt-auto">
            <div className="flex items-center justify-between">
              <div className="flex flex-col">
                <span className="text-purple-400 font-bold text-sm">
                  ${effectivePrice.toFixed(2)}
                </span>
                {discount && (
                  <span className="text-gray-500 line-through text-xs">
                    ${originalPrice.toFixed(2)}
                  </span>
                )}
              </div>
              <button className="bg-purple-600 hover:bg-purple-700 text-white p-1.5 rounded-full transition-colors">
                <ShoppingCart className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}

// Helper functions (moved outside component to avoid re-creation)
function getEffectivePrice(product: ProductWithCategory) {
  if (product.discount_price) return product.discount_price
  return product.user_price || 0
}

function getOriginalPrice(product: ProductWithCategory) {
  return product.user_price || 0
}

function hasDiscount(product: ProductWithCategory) {
  return product.discount_price && product.user_price && product.discount_price < product.user_price
}

function getDiscountPercentage(product: ProductWithCategory) {
  if (!hasDiscount(product)) return 0
  const original = product.user_price!
  const discounted = product.discount_price!
  return Math.round(((original - discounted) / original) * 100)
}
