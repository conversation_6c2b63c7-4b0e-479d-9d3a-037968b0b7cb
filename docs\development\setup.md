# Development Setup Guide

## 🚀 Quick Start

This guide will help you set up the Bentakon e-commerce system for local development.

## 📋 Prerequisites

### Required Software

- **Node.js** 18.0 or higher
- **npm** or **yarn** package manager
- **Git** for version control
- **PostgreSQL** 14+ (or Supabase account)
- **Code Editor** (VS Code recommended)

### Recommended Tools

- **Supabase CLI** for database management
- **Postman** or **Insomnia** for API testing
- **pgAdmin** or **DBeaver** for database administration

## 🛠️ Installation Steps

### 1. Clone Repository

```bash
git clone https://github.com/your-org/bentakon-store.git
cd bentakon-store
```

### 2. Install Dependencies

```bash
# Using npm
npm install

# Using yarn
yarn install
```

### 3. Environment Configuration

Create environment files:

```bash
cp .env.example .env.local
```

Configure `.env.local`:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/bentakon

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
TENANT_ID=default

# Security
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000

# Optional: External Services
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 4. Database Setup

#### Option A: Using Supabase (Recommended)

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Copy your project URL and keys to `.env.local`
3. Run the database schema:

```bash
# Using Supabase CLI
supabase db reset

# Or manually execute the schema
psql -h db.your-project.supabase.co -U postgres -d postgres -f lib/database-schema.sql
```

#### Option B: Local PostgreSQL

1. Install PostgreSQL locally
2. Create database:

```sql
CREATE DATABASE bentakon;
CREATE USER bentakon_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE bentakon TO bentakon_user;
```

3. Run schema:

```bash
psql -U bentakon_user -d bentakon -f lib/database-schema.sql
```

### 5. Verify Setup

```bash
# Start development server
npm run dev

# Open browser
open http://localhost:3000
```

## 🔧 Development Scripts

### Available Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks

# Database
npm run db:generate  # Generate database types
npm run db:reset     # Reset database schema
npm run db:seed      # Seed with sample data

# Testing
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:e2e     # Run end-to-end tests
```

### Development Workflow

```bash
# 1. Pull latest changes
git pull origin main

# 2. Install new dependencies
npm install

# 3. Update database if needed
npm run db:reset

# 4. Start development
npm run dev

# 5. Make changes and test
npm run lint
npm run type-check
npm run test

# 6. Commit changes
git add .
git commit -m "feat: add new feature"
git push origin feature-branch
```

## 🗄️ Database Development

### Schema Management

```bash
# Generate TypeScript types from database
npm run db:generate

# This creates/updates:
# - lib/database.types.ts
# - app/types/database.ts
```

### Sample Data

```bash
# Seed database with sample data
npm run db:seed

# This creates:
# - Sample products and packages
# - Test users with different roles
# - Sample orders and transactions
```

### Database Migrations

```bash
# Create new migration
supabase migration new add_new_feature

# Apply migrations
supabase db push

# Reset to clean state
supabase db reset
```

## 🧪 Testing Setup

### Unit Tests

```bash
# Install testing dependencies
npm install --save-dev jest @testing-library/react @testing-library/jest-dom

# Run tests
npm run test

# Watch mode
npm run test:watch
```

### API Testing

Create test files in `__tests__/api/`:

```javascript
// __tests__/api/auth.test.js
import { createMocks } from 'node-mocks-http'
import handler from '../../pages/api/auth/login'

describe('/api/auth/login', () => {
  it('should authenticate valid user', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        email: '<EMAIL>',
        password: 'password123'
      }
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(200)
    expect(JSON.parse(res._getData())).toMatchObject({
      success: true
    })
  })
})
```

### E2E Testing

```bash
# Install Playwright
npm install --save-dev @playwright/test

# Run E2E tests
npm run test:e2e
```

## 🔍 Debugging

### VS Code Configuration

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node-terminal",
      "request": "launch",
      "command": "npm run dev"
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000"
    }
  ]
}
```

### Database Debugging

```bash
# Connect to database
psql -h localhost -U bentakon_user -d bentakon

# Useful queries
\dt                    # List tables
\d users              # Describe users table
SELECT * FROM users;  # Query users

# Check RLS policies
SELECT * FROM pg_policies WHERE schemaname = 'public';
```

### API Debugging

```javascript
// Add debug logging to API routes
console.log('Request body:', req.body)
console.log('User:', user)
console.log('Query result:', data)

// Use debugger
debugger; // Will pause execution in VS Code
```

## 🔒 Security Setup

### Environment Security

```bash
# Never commit .env files
echo ".env*" >> .gitignore

# Use different keys for different environments
# Development: Use test keys
# Production: Use secure, unique keys
```

### Database Security

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Test RLS policies
SET LOCAL "request.jwt.claims" = '{"sub": "user-id", "role": "user"}';
SELECT * FROM orders; -- Should only return user's orders
```

## 📊 Monitoring Setup

### Development Monitoring

```javascript
// Add request logging middleware
export function middleware(request: NextRequest) {
  console.log(`${request.method} ${request.url}`)
  return NextResponse.next()
}
```

### Error Tracking

```javascript
// Add error boundary
export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  console.error('Global error:', error)
  
  return (
    <html>
      <body>
        <h2>Something went wrong!</h2>
        <button onClick={() => reset()}>Try again</button>
      </body>
    </html>
  )
}
```

## 🚀 Deployment Preparation

### Build Verification

```bash
# Test production build
npm run build
npm run start

# Check for build errors
npm run lint
npm run type-check
```

### Environment Variables

```bash
# Production environment variables
NEXT_PUBLIC_SUPABASE_URL=https://prod-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=prod-service-key
NEXTAUTH_SECRET=secure-production-secret
```

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Issues

```bash
# Check database connection
psql -h your-host -U your-user -d your-db

# Verify environment variables
echo $NEXT_PUBLIC_SUPABASE_URL
```

#### Build Errors

```bash
# Clear Next.js cache
rm -rf .next

# Clear node modules
rm -rf node_modules
npm install
```

#### Type Errors

```bash
# Regenerate database types
npm run db:generate

# Check TypeScript configuration
npx tsc --noEmit
```

### Getting Help

1. **Check Documentation**: Review relevant docs sections
2. **Search Issues**: Look for similar problems in GitHub issues
3. **Debug Logs**: Check browser console and server logs
4. **Database Logs**: Check Supabase dashboard for database errors
5. **Ask Team**: Reach out to team members for assistance

## 📚 Additional Resources

### Documentation Links

- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### Development Tools

- [Supabase Dashboard](https://app.supabase.com)
- [Vercel Dashboard](https://vercel.com/dashboard)
- [GitHub Repository](https://github.com/your-org/bentakon-store)

### Community

- [Next.js Discord](https://discord.gg/nextjs)
- [Supabase Discord](https://discord.supabase.com)
- [Team Slack/Discord](your-team-chat-link)

---

**Ready to develop!** 🎉 Your local environment should now be fully configured and ready for development.
