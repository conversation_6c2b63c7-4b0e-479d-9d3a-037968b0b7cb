import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()

    // First try to get tenant from headers (set by middleware)
    let tenantId = request.headers.get('x-tenant-id')

    // If no tenant in headers, try to get from user profile
    if (!tenantId) {
      try {
        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          const { data: profile } = await supabase
            .from('users')
            .select('tenant_id')
            .eq('id', user.id)
            .single()

          tenantId = profile?.tenant_id
        }
      } catch (authError) {
        // No authenticated user, will use main tenant fallback
      }
    }

    // If still no tenant, get main tenant as fallback
    if (!tenantId) {
      const { data: mainTenant } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', 'main')
        .single()

      tenantId = mainTenant?.id || 'caf1844f-4cc2-4c17-a775-1c837ae01051'
    }

    // Get active currencies for this tenant
    const { data: currencies, error } = await supabase
      .from('currencies')
      .select(`
        code,
        name,
        exchange_rate,
        is_active,
        created_at,
        updated_at
      `)
      .eq('tenant_id', tenantId)
      .eq('is_active', true)
      .order('code')

    if (error) {
      console.error('Database error fetching currencies:', error)
      // Return USD as fallback
      return NextResponse.json({
        currencies: [
          {
            code: 'USD',
            name: 'US Dollar',
            exchange_rate: 1.0,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ],
        fallback: true,
        error: error.message
      })
    }

    const currencyList = currencies || []

    // Ensure USD is always available
    const hasUSD = currencyList.some(c => c.code === 'USD')
    if (!hasUSD) {
      currencyList.unshift({
        code: 'USD',
        name: 'US Dollar',
        exchange_rate: 1.0,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }

    return NextResponse.json({
      currencies: currencyList,
      tenantId,
      count: currencyList.length
    })
  } catch (error) {
    console.error('Error fetching currencies:', error)

    // Return USD as ultimate fallback
    return NextResponse.json({
      currencies: [
        {
          code: 'USD',
          name: 'US Dollar',
          exchange_rate: 1.0,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ],
      fallback: true,
      error: 'Failed to fetch currencies'
    })
  }
}
