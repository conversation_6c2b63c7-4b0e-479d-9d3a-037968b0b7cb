# Row Level Security (RLS) Policies Documentation

## 🔒 Overview

Row Level Security (RLS) is the primary security mechanism in the Bentakon system. It ensures that users can only access data they're authorized to see, enforced at the database level.

## 🎯 Security Principles

### **1. Multi-Tenant Isolation**
Every table includes `tenant_id` to separate data between different customers/organizations.

### **2. Role-Based Access Control**
User roles (admin, worker, distributor, user) determine what data can be accessed and modified.

### **3. Data Ownership**
Users can only access their own personal data (orders, transactions, wallet).

### **4. Defense in Depth**
RLS works alongside API-level security checks for comprehensive protection.

## 📋 RLS Policy Structure

### **Policy Types**
- **SELECT**: Controls read access
- **INSERT**: Controls creation of new records
- **UPDATE**: Controls modification of existing records
- **DELETE**: Controls deletion of records
- **ALL**: Applies to all operations

### **Policy Components**
```sql
CREATE POLICY "policy_name" ON table_name
FOR operation_type
USING (condition_for_existing_rows)
WITH CHECK (condition_for_new_rows);
```

## 🏗️ Table-by-Table RLS Policies

### 1. Users Table

```sql
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Users can read their own profile
CREATE POLICY "Users can read own data" ON users 
FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own data" ON users 
FOR UPDATE USING (auth.uid() = id);

-- Admins can read all users
CREATE POLICY "Admins can read all users" ON users 
FOR SELECT USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);

-- Admins can update user roles
CREATE POLICY "Admins can update users" ON users 
FOR UPDATE USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
```

**Explanation**:
- Users can only see/edit their own profile
- Admins have full access to all user data
- Role changes require admin privileges

### 2. Products Table

```sql
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Products are readable by everyone (public catalog)
CREATE POLICY "Products are readable by everyone" ON products 
FOR SELECT USING (active = true);

-- Only admins can manage products
CREATE POLICY "Admins can manage products" ON products 
FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
```

**Explanation**:
- All users can browse active products
- Only admins can create, update, or delete products
- Inactive products are hidden from non-admins

### 3. Packages Table

```sql
ALTER TABLE packages ENABLE ROW LEVEL SECURITY;

-- Packages are readable by everyone
CREATE POLICY "Packages are readable by everyone" ON packages 
FOR SELECT USING (active = true);

-- Only admins can manage packages
CREATE POLICY "Admins can manage packages" ON packages 
FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
```

### 4. Orders Table

```sql
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

-- Users can read their own orders
CREATE POLICY "Users can read own orders" ON orders 
FOR SELECT USING (auth.uid() = user_id);

-- Users can create orders for themselves
CREATE POLICY "Users can create orders" ON orders 
FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Workers and admins can read all orders
CREATE POLICY "Workers can read all orders" ON orders 
FOR SELECT USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'worker'))
);

-- Workers and admins can update orders
CREATE POLICY "Workers can update orders" ON orders 
FOR UPDATE USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'worker'))
);
```

**Explanation**:
- Users see only their own orders
- Users can only create orders for themselves
- Workers can view and update all orders for processing
- Admins have full access

### 5. Digital Codes Table

```sql
ALTER TABLE digital_codes ENABLE ROW LEVEL SECURITY;

-- Codes are readable by everyone (for availability checking)
CREATE POLICY "Digital codes are readable" ON digital_codes 
FOR SELECT USING (true);

-- Only admins can manage digital codes
CREATE POLICY "Admins can manage digital codes" ON digital_codes 
FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);

-- Workers can assign codes to orders
CREATE POLICY "Workers can assign codes" ON digital_codes 
FOR UPDATE USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'worker'))
);
```

**Explanation**:
- Codes are visible for inventory checking
- Only admins can create/delete codes
- Workers can assign codes to orders

### 6. Wallets Table

```sql
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;

-- Users can read their own wallet
CREATE POLICY "Users can read own wallet" ON wallets 
FOR SELECT USING (auth.uid() = user_id);

-- Admins can read all wallets
CREATE POLICY "Admins can read all wallets" ON wallets 
FOR SELECT USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);

-- System can manage wallets (for database functions)
CREATE POLICY "System can manage wallets" ON wallets 
FOR ALL USING (auth.uid() IS NOT NULL);
```

**Explanation**:
- Users see only their own wallet balance
- Admins can view all wallet balances
- Database functions can update wallets

### 7. Transactions Table

```sql
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Users can read their own transactions
CREATE POLICY "Users can read own transactions" ON transactions 
FOR SELECT USING (auth.uid() = user_id);

-- Admins can read all transactions
CREATE POLICY "Admins can read all transactions" ON transactions 
FOR SELECT USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);

-- System can create transactions (for database functions)
CREATE POLICY "System can create transactions" ON transactions 
FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);
```

**Explanation**:
- Users see only their own transaction history
- Admins can view all financial transactions
- Database functions can create transactions

### 8. Currencies Table

```sql
ALTER TABLE currencies ENABLE ROW LEVEL SECURITY;

-- Currencies are readable by everyone
CREATE POLICY "Currencies are readable by everyone" ON currencies 
FOR SELECT USING (is_active = true);

-- Only admins can manage currencies
CREATE POLICY "Admins can manage currencies" ON currencies 
FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
```

### 9. Custom Fields Tables

```sql
ALTER TABLE product_custom_fields ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_field_options ENABLE ROW LEVEL SECURITY;

-- Custom fields are readable by everyone
CREATE POLICY "Custom fields are readable" ON product_custom_fields 
FOR SELECT USING (true);

CREATE POLICY "Field options are readable" ON custom_field_options 
FOR SELECT USING (true);

-- Only admins can manage custom fields
CREATE POLICY "Admins can manage custom fields" ON product_custom_fields 
FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);

CREATE POLICY "Admins can manage field options" ON custom_field_options 
FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
```

## 🔐 Multi-Tenant Isolation

### **Tenant-Scoped Policies**
For tables with `tenant_id`, policies should include tenant filtering:

```sql
-- Example: Tenant-aware product policy
CREATE POLICY "Products scoped to tenant" ON products 
FOR SELECT USING (
  tenant_id = current_setting('app.current_tenant', true)
  AND active = true
);
```

### **Setting Tenant Context**
```javascript
// In API routes, set tenant context
await supabase.rpc('set_config', {
  parameter: 'app.current_tenant',
  value: getCurrentTenant()
})
```

## 🎭 Role-Based Access Matrix

| Resource | Admin | Worker | Distributor | User |
|----------|-------|--------|-------------|------|
| **Users** | Full CRUD | Read own | Read own | Read own |
| **Products** | Full CRUD | Read all | Read all | Read active |
| **Packages** | Full CRUD | Read all | Read all | Read active |
| **Orders** | Full CRUD | Read/Update all | Read own | Read/Create own |
| **Digital Codes** | Full CRUD | Assign only | Read assigned | Read assigned |
| **Wallets** | Read all | Read own | Read own | Read own |
| **Transactions** | Read all | Read own | Read own | Read own |
| **Currencies** | Full CRUD | Read active | Read active | Read active |
| **Custom Fields** | Full CRUD | Read all | Read all | Read all |

## 🧪 Testing RLS Policies

### **Test User Access**
```sql
-- Set user context
SET LOCAL "request.jwt.claims" = '{"sub": "user-uuid-here", "role": "user"}';

-- Test queries
SELECT * FROM orders; -- Should only return user's orders
SELECT * FROM wallets; -- Should only return user's wallet
```

### **Test Admin Access**
```sql
-- Set admin context
SET LOCAL "request.jwt.claims" = '{"sub": "admin-uuid-here", "role": "admin"}';

-- Test queries
SELECT * FROM orders; -- Should return all orders
SELECT * FROM users; -- Should return all users
```

### **Test Policy Violations**
```sql
-- Try to access another user's data
SELECT * FROM orders WHERE user_id != auth.uid(); -- Should return empty
```

## ⚠️ Security Best Practices

### **1. Always Enable RLS**
```sql
-- Enable RLS on every table
ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;
```

### **2. Test Policies Thoroughly**
- Test each role's access patterns
- Verify data isolation between tenants
- Test edge cases and error conditions

### **3. Use Least Privilege**
- Grant minimum necessary permissions
- Prefer specific policies over broad access
- Regularly audit policy effectiveness

### **4. Monitor Policy Performance**
- RLS policies can impact query performance
- Use EXPLAIN ANALYZE to check query plans
- Optimize policies for common access patterns

### **5. Handle Policy Errors**
```javascript
// Handle RLS policy violations gracefully
try {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
  
  if (error && error.code === 'PGRST116') {
    // RLS policy violation
    console.log('Access denied by security policy')
  }
} catch (error) {
  console.error('Database error:', error)
}
```

## 🔧 Policy Maintenance

### **Adding New Policies**
1. Identify the security requirements
2. Write the policy with minimal permissions
3. Test with different user roles
4. Deploy and monitor performance

### **Updating Existing Policies**
1. Backup current policies
2. Test changes in development
3. Use `CREATE OR REPLACE POLICY`
4. Verify no access regressions

### **Debugging Policy Issues**
```sql
-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- View all policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE schemaname = 'public';
```
