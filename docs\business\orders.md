# Order Workflow Documentation

## 🔄 Overview

The order system manages the complete customer purchase journey from product selection to delivery, with automated financial operations and role-based processing.

## 📋 Order Lifecycle

```
Product Selection → Pricing Calculation → Wallet Deduction → Order Creation
        ↓
Order Pending → Worker Processing → Accept/Decline Decision
        ↓
Accepted: Code Assignment → Completion
Declined: Automatic Refund → Closure
```

## 🏗️ Order States

### 1. Pending (معلق)

**Description**: Order created, payment deducted, awaiting worker processing.

**Characteristics**:
- Customer payment has been deducted from wallet
- Order is in the processing queue
- Worker can accept or decline
- Automatic refund if declined

**Duration**: Typically 1-24 hours depending on worker availability

### 2. Accepted (مقبول)

**Description**: Worker approved the order, processing for delivery.

**Characteristics**:
- Worker has reviewed and approved the order
- Digital code assigned if applicable
- Customer notified of acceptance
- Order moves toward completion

**Next Steps**: Code delivery or manual fulfillment

### 3. Declined (مرفوض)

**Description**: Worker rejected the order with reason.

**Characteristics**:
- Worker provided decline reason in notes
- Automatic refund processed to customer wallet
- Order marked as closed
- Customer notified with reason

**Financial Impact**: Full refund issued automatically

### 4. Completed (مكتمل)

**Description**: Order successfully fulfilled and delivered.

**Characteristics**:
- Digital code delivered to customer (if applicable)
- Order marked as final state
- Customer can access purchased content
- Revenue and profit recorded

**Final State**: No further processing required

## 💰 Financial Flow

### Order Creation Financial Process

```javascript
// 1. Calculate final price based on user role and product
const finalPrice = calculateOrderPrice(product, package, userRole, pricingTier)

// 2. Validate wallet balance
const walletBalance = await getWalletBalance(userId)
if (walletBalance < finalPrice) {
  throw new Error('رصيد المحفظة غير كافي')
}

// 3. Deduct payment from wallet
const transactionId = await supabase.rpc('process_purchase', {
  user_uuid: userId,
  amount_val: finalPrice,
  order_uuid: null, // Will be updated after order creation
  description_val: `شراء ${product.name}`
})

// 4. Create order record
const order = await supabase.from('orders').insert({
  user_id: userId,
  product_id: productId,
  package_id: packageId,
  amount: finalPrice,
  cost: originalPrice,
  profit: finalPrice - originalPrice,
  status: 'pending',
  pricing_tier: pricingTier,
  custom_field_data: customData
})

// 5. Link transaction to order
await supabase.from('transactions')
  .update({ reference_id: order.id })
  .eq('id', transactionId)
```

### Refund Process (Declined Orders)

```javascript
// Automatic refund when order is declined
if (newStatus === 'declined') {
  await supabase.rpc('process_refund', {
    user_uuid: order.user_id,
    amount_val: order.amount,
    order_uuid: order.id,
    description_val: `استرداد للطلب المرفوض: ${declineReason}`
  })
}
```

## 🎯 Pricing Calculation

### Multi-Tier Pricing Logic

```javascript
function calculateOrderPrice(product: Product, package: Package | null, userRole: string, pricingTier: string) {
  // Determine pricing source (product or package)
  const pricingSource = package || product
  
  // Calculate based on pricing tier and user role
  let finalPrice: number
  
  switch (pricingTier) {
    case 'distributor':
      if (userRole === 'distributor') {
        finalPrice = pricingSource.distributor_price || pricingSource.user_price
      } else {
        throw new Error('غير مصرح بالتسعير الخاص بالموزعين')
      }
      break
      
    case 'discount':
      finalPrice = pricingSource.discount_price || pricingSource.user_price
      break
      
    case 'user':
    default:
      finalPrice = pricingSource.user_price
      break
  }
  
  return {
    finalPrice,
    originalPrice: pricingSource.original_price,
    profit: finalPrice - pricingSource.original_price
  }
}
```

### Pricing Examples

| User Role | Product Price | Package Price | Final Price | Profit |
|-----------|---------------|---------------|-------------|--------|
| User | $50 (user) | - | $50 | $30 |
| Distributor | $50 (user), $40 (dist) | - | $40 | $20 |
| User (discount) | $50 (user), $35 (disc) | - | $35 | $15 |

## 🔧 Worker Processing

### Order Queue Management

```javascript
// Get pending orders for worker processing
export async function GET(request: NextRequest) {
  const { user, profile } = await checkUserRole(['admin', 'worker'])
  
  const { data: orders } = await supabase
    .from('orders')
    .select(`
      *,
      products (name, image_url, slug),
      packages (name, image_url),
      users!orders_user_id_fkey (name, email)
    `)
    .eq('status', 'pending')
    .eq('tenant_id', getCurrentTenant())
    .order('created_at', { ascending: true }) // FIFO processing

  return NextResponse.json({
    success: true,
    data: { orders }
  })
}
```

### Order Processing Actions

```javascript
// Accept order
async function acceptOrder(orderId: string, workerId: string, note?: string) {
  // Update order status
  const { data: order } = await supabase
    .from('orders')
    .update({
      status: 'accepted',
      handled_by: workerId,
      note: note || 'تم قبول الطلب',
      updated_at: new Date().toISOString()
    })
    .eq('id', orderId)
    .single()

  // Assign digital code if product has codes
  if (order.product.has_codes) {
    await assignDigitalCode(orderId, order.product_id, order.package_id)
  }

  return order
}

// Decline order with automatic refund
async function declineOrder(orderId: string, workerId: string, reason: string) {
  const { data: order } = await supabase
    .from('orders')
    .select('*')
    .eq('id', orderId)
    .single()

  // Process refund
  await supabase.rpc('process_refund', {
    user_uuid: order.user_id,
    amount_val: order.amount,
    order_uuid: order.id,
    description_val: `استرداد للطلب المرفوض: ${reason}`
  })

  // Update order status
  await supabase
    .from('orders')
    .update({
      status: 'declined',
      handled_by: workerId,
      note: reason,
      updated_at: new Date().toISOString()
    })
    .eq('id', orderId)

  return order
}
```

## 🎮 Digital Code Assignment

### Code Assignment Logic

```javascript
async function assignDigitalCode(orderId: string, productId: string, packageId?: string) {
  // Find available digital code
  let query = supabase
    .from('digital_codes')
    .select('id, code')
    .eq('is_used', false)
    .is('order_id', null)
    .limit(1)

  // Check product-level or package-level codes
  if (packageId) {
    query = query.eq('package_id', packageId)
  } else {
    query = query.eq('product_id', productId)
  }

  const { data: availableCode } = await query.single()

  if (!availableCode) {
    throw new Error('لا توجد أكواد متاحة لهذا المنتج')
  }

  // Assign code to order
  await supabase
    .from('digital_codes')
    .update({
      order_id: orderId,
      is_used: true,
      used_at: new Date().toISOString()
    })
    .eq('id', availableCode.id)

  // Update order with code reference
  await supabase
    .from('orders')
    .update({ code_id: availableCode.id })
    .eq('id', orderId)

  return availableCode
}
```

### Code Delivery

```javascript
// Customer can view their digital codes
export async function GET(request: NextRequest) {
  const { data: { user } } = await supabase.auth.getUser()
  
  const { data: orders } = await supabase
    .from('orders')
    .select(`
      id,
      status,
      digital_codes (code, is_used),
      products (name),
      packages (name)
    `)
    .eq('user_id', user.id)
    .eq('status', 'completed')
    .not('code_id', 'is', null)

  return NextResponse.json({
    success: true,
    data: { orders }
  })
}
```

## 📊 Order Analytics

### Order Statistics

```sql
-- Order status distribution
SELECT 
  status,
  COUNT(*) as order_count,
  SUM(amount) as total_revenue,
  AVG(amount) as avg_order_value
FROM orders 
WHERE tenant_id = 'default'
  AND created_at >= NOW() - INTERVAL '30 days'
GROUP BY status
ORDER BY order_count DESC;
```

### Worker Performance

```sql
-- Worker processing statistics
SELECT 
  u.name as worker_name,
  COUNT(o.id) as orders_processed,
  COUNT(CASE WHEN o.status = 'accepted' THEN 1 END) as orders_accepted,
  COUNT(CASE WHEN o.status = 'declined' THEN 1 END) as orders_declined,
  ROUND(
    COUNT(CASE WHEN o.status = 'accepted' THEN 1 END) * 100.0 / COUNT(o.id), 
    2
  ) as acceptance_rate
FROM users u
JOIN orders o ON u.id = o.handled_by
WHERE u.role = 'worker'
  AND o.created_at >= NOW() - INTERVAL '30 days'
GROUP BY u.id, u.name
ORDER BY orders_processed DESC;
```

### Revenue Analysis

```sql
-- Daily revenue and profit
SELECT 
  DATE(created_at) as order_date,
  COUNT(*) as orders_count,
  SUM(amount) as total_revenue,
  SUM(profit) as total_profit,
  ROUND(SUM(profit) * 100.0 / SUM(amount), 2) as profit_margin
FROM orders 
WHERE status = 'completed'
  AND tenant_id = 'default'
  AND created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY order_date DESC;
```

## 🔔 Order Notifications

### Customer Notifications

```javascript
// Notify customer of order status changes
async function notifyCustomer(orderId: string, status: string, message: string) {
  const { data: order } = await supabase
    .from('orders')
    .select(`
      *,
      users (email, name),
      products (name)
    `)
    .eq('id', orderId)
    .single()

  // Send notification (email, SMS, push notification)
  await sendNotification({
    to: order.users.email,
    subject: `تحديث حالة الطلب #${order.id.slice(-8)}`,
    message: `${order.users.name}، ${message}`,
    order_id: orderId,
    status: status
  })
}

// Usage examples
await notifyCustomer(orderId, 'accepted', 'تم قبول طلبك وسيتم تسليمه قريباً')
await notifyCustomer(orderId, 'declined', 'تم رفض طلبك وإعادة المبلغ إلى محفظتك')
await notifyCustomer(orderId, 'completed', 'تم تسليم طلبك بنجاح')
```

### Worker Notifications

```javascript
// Notify workers of new orders
async function notifyWorkers(orderId: string) {
  const { data: workers } = await supabase
    .from('users')
    .select('id, email, name')
    .eq('role', 'worker')
    .eq('tenant_id', getCurrentTenant())

  for (const worker of workers) {
    await sendNotification({
      to: worker.email,
      subject: 'طلب جديد يحتاج للمعالجة',
      message: `يوجد طلب جديد #${orderId.slice(-8)} في انتظار المعالجة`,
      order_id: orderId,
      worker_id: worker.id
    })
  }
}
```

## 🧪 Testing Order Workflow

### Test Order Creation

```javascript
// Test complete order flow
async function testOrderCreation() {
  // 1. Create order
  const response = await fetch('/api/orders', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      product_id: 'product-uuid',
      package_id: 'package-uuid',
      pricing_tier: 'user',
      custom_field_data: {
        player_id: '12345',
        server: 'EU-West'
      }
    })
  })

  const order = await response.json()
  console.log('Order created:', order)

  // 2. Test worker processing
  const processResponse = await fetch(`/api/worker/orders/${order.data.id}`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${workerToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      status: 'accepted',
      note: 'Order processed successfully'
    })
  })

  const processedOrder = await processResponse.json()
  console.log('Order processed:', processedOrder)
}
```

### Test Refund Process

```javascript
// Test automatic refund on decline
async function testOrderDecline() {
  const response = await fetch(`/api/worker/orders/${orderId}`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${workerToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      status: 'declined',
      note: 'Product temporarily out of stock'
    })
  })

  const result = await response.json()
  console.log('Order declined and refunded:', result)
}
```

## 🔧 Order Management Best Practices

### 1. Processing Time Guidelines

- **Pending Orders**: Process within 24 hours
- **High-Value Orders**: Priority processing within 4 hours
- **Bulk Orders**: May require 48-72 hours

### 2. Decline Reasons

Common decline reasons with standard messages:
- "المنتج غير متوفر مؤقتاً" (Product temporarily unavailable)
- "بيانات العميل غير صحيحة" (Incorrect customer data)
- "مشكلة في الدفع" (Payment issue)
- "طلب مشبوه" (Suspicious order)

### 3. Quality Assurance

- Verify customer data before acceptance
- Check digital code availability
- Validate custom field requirements
- Confirm product availability

### 4. Customer Communication

- Always provide clear decline reasons
- Respond to customer inquiries promptly
- Maintain professional communication
- Follow up on completed orders
