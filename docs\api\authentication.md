# Authentication API Documentation

## 🔐 Overview

The authentication system uses Supa<PERSON> Auth with custom user profiles and role-based access control. All authentication endpoints handle user registration, login, and profile management.

## 🏗️ Authentication Flow

```
1. User Registration → Supabase Auth → User Profile Creation → Wallet Creation
2. User Login → JWT Token → Role Verification → API Access
3. Protected Routes → Token Validation → RLS Policy Check → Data Access
```

## 📡 API Endpoints

### 1. User Registration

**Endpoint**: `POST /api/auth/register`

**Purpose**: Creates new user account with profile and wallet initialization.

**Request Body**:
```json
{
  "name": "أحمد محمد",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!"
}
```

**Validation Rules**:
- `name`: 2-50 characters, required
- `email`: Valid email format, unique
- `password`: Min 8 chars, must contain uppercase, lowercase, and number
- `confirmPassword`: Must match password

**Response Success** (201):
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-here",
      "email": "<EMAIL>",
      "email_confirmed_at": null
    },
    "session": {
      "access_token": "jwt-token-here",
      "refresh_token": "refresh-token-here"
    }
  },
  "message": "تم إنشاء الحساب بنجاح"
}
```

**Response Error** (400):
```json
{
  "success": false,
  "error": "البريد الإلكتروني مستخدم بالفعل",
  "details": [
    {
      "code": "email_address_invalid",
      "message": "Invalid email address"
    }
  ]
}
```

**Backend Process**:
1. Validate input data with Zod schema
2. Create Supabase Auth user
3. Create user profile in `users` table
4. Initialize wallet with `create_user_wallet()` function
5. Return authentication tokens

**Implementation**:
```javascript
// Registration API route
export async function POST(request: NextRequest) {
  const body = await request.json()
  
  // Validate input
  const validationResult = userRegistrationSchema.safeParse(body)
  if (!validationResult.success) {
    return NextResponse.json({
      success: false,
      error: 'بيانات غير صالحة',
      details: validationResult.error.errors
    }, { status: 400 })
  }

  const { name, email, password } = validationResult.data
  const supabase = await createSupabaseServerClient()

  // Create auth user
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: { name }
    }
  })

  if (error) {
    return NextResponse.json({
      success: false,
      error: getErrorMessage(error.message)
    }, { status: 400 })
  }

  if (data.user) {
    // Create user profile
    await supabase.from('users').insert({
      id: data.user.id,
      tenant_id: getCurrentTenant(),
      email,
      name,
      role: 'user'
    })

    // Create wallet
    await supabase.rpc('create_user_wallet', {
      user_uuid: data.user.id,
      tenant_uuid: getCurrentTenant()
    })
  }

  return NextResponse.json({
    success: true,
    data,
    message: 'تم إنشاء الحساب بنجاح'
  })
}
```

### 2. User Login

**Endpoint**: `POST /api/auth/login`

**Purpose**: Authenticates existing users and returns session tokens.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Response Success** (200):
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-here",
      "email": "<EMAIL>",
      "role": "user",
      "name": "أحمد محمد"
    },
    "session": {
      "access_token": "jwt-token-here",
      "refresh_token": "refresh-token-here"
    }
  },
  "message": "تم تسجيل الدخول بنجاح"
}
```

**Response Error** (401):
```json
{
  "success": false,
  "error": "البريد الإلكتروني أو كلمة المرور غير صحيحة"
}
```

**Backend Process**:
1. Validate email and password
2. Authenticate with Supabase Auth
3. Fetch user profile and role
4. Return user data and tokens

### 3. User Logout

**Endpoint**: `POST /api/auth/logout`

**Purpose**: Invalidates user session and clears tokens.

**Headers**:
```
Authorization: Bearer jwt-token-here
```

**Response Success** (200):
```json
{
  "success": true,
  "message": "تم تسجيل الخروج بنجاح"
}
```

### 4. Get Current User

**Endpoint**: `GET /api/auth/user`

**Purpose**: Returns current authenticated user information.

**Headers**:
```
Authorization: Bearer jwt-token-here
```

**Response Success** (200):
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-here",
      "email": "<EMAIL>",
      "name": "أحمد محمد",
      "role": "user",
      "tenant_id": "default",
      "created_at": "2024-01-01T00:00:00Z"
    },
    "wallet": {
      "balance": 150.50,
      "currency": "USD"
    }
  }
}
```

**Response Error** (401):
```json
{
  "success": false,
  "error": "غير مصرح"
}
```

## 🎭 Role-Based Access Control

### User Roles

| Role | Description | Permissions |
|------|-------------|-------------|
| **admin** | System administrator | Full access to all features |
| **worker** | Order processor | Can manage orders, assign codes |
| **distributor** | Bulk buyer | Special pricing, bulk operations |
| **user** | Regular customer | Browse products, make purchases |

### Role Verification Middleware

```javascript
// Check user role in API routes
async function checkUserRole(requiredRoles: string[]) {
  const supabase = await createSupabaseServerClient()
  
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error || !user) {
    throw new Error('غير مصرح')
  }

  const { data: profile } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single()

  if (!profile || !requiredRoles.includes(profile.role)) {
    throw new Error('غير مصرح - صلاحيات غير كافية')
  }

  return { user, profile }
}

// Usage in API routes
export async function POST(request: NextRequest) {
  try {
    const { user, profile } = await checkUserRole(['admin', 'worker'])
    // Proceed with admin/worker-only logic
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 403 })
  }
}
```

## 🔒 Security Features

### 1. JWT Token Validation

All protected routes validate JWT tokens:

```javascript
// Server-side token validation
const supabase = await createSupabaseServerClient()
const { data: { user }, error } = await supabase.auth.getUser()

if (error || !user) {
  return NextResponse.json({
    success: false,
    error: 'غير مصرح'
  }, { status: 401 })
}
```

### 2. Multi-Tenant Isolation

Users are automatically scoped to their tenant:

```javascript
// Get current tenant from context
function getCurrentTenant(): string {
  // In production, this would come from subdomain, header, or JWT
  return process.env.TENANT_ID || 'default'
}

// Apply tenant filter to all queries
function addTenantFilter(query: any) {
  return query.eq('tenant_id', getCurrentTenant())
}
```

### 3. Password Security

- Minimum 8 characters
- Must contain uppercase, lowercase, and number
- Handled by Supabase Auth with bcrypt hashing
- Password reset via email verification

### 4. Rate Limiting

```javascript
// Implement rate limiting for auth endpoints
const rateLimiter = new Map()

function checkRateLimit(ip: string, limit: number = 5) {
  const now = Date.now()
  const windowStart = now - (15 * 60 * 1000) // 15 minutes
  
  if (!rateLimiter.has(ip)) {
    rateLimiter.set(ip, [])
  }
  
  const requests = rateLimiter.get(ip)
  const recentRequests = requests.filter((time: number) => time > windowStart)
  
  if (recentRequests.length >= limit) {
    throw new Error('تم تجاوز الحد المسموح من المحاولات')
  }
  
  recentRequests.push(now)
  rateLimiter.set(ip, recentRequests)
}
```

## 🧪 Testing Authentication

### Test User Registration

```javascript
// Test registration
const response = await fetch('/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'Test User',
    email: '<EMAIL>',
    password: 'TestPass123!',
    confirmPassword: 'TestPass123!'
  })
})

const result = await response.json()
console.log('Registration result:', result)
```

### Test User Login

```javascript
// Test login
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'TestPass123!'
  })
})

const result = await response.json()
const token = result.data.session.access_token
```

### Test Protected Route

```javascript
// Test protected route access
const response = await fetch('/api/admin/dashboard', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})

const result = await response.json()
console.log('Protected route result:', result)
```

## 🔧 Error Handling

### Common Error Codes

| Code | Message | Description |
|------|---------|-------------|
| `email_address_invalid` | Invalid email | Email format is incorrect |
| `email_address_not_authorized` | Email not authorized | Email domain not allowed |
| `weak_password` | Password too weak | Password doesn't meet requirements |
| `email_address_taken` | Email already exists | User already registered |
| `invalid_credentials` | Invalid login | Wrong email/password combination |
| `email_not_confirmed` | Email not verified | User needs to verify email |

### Error Response Format

```json
{
  "success": false,
  "error": "رسالة الخطأ بالعربية",
  "code": "error_code",
  "details": [
    {
      "field": "email",
      "message": "البريد الإلكتروني غير صالح"
    }
  ]
}
```

## 🔄 Session Management

### Token Refresh

```javascript
// Automatic token refresh
const { data, error } = await supabase.auth.refreshSession()
if (error) {
  // Redirect to login
  window.location.href = '/login'
}
```

### Session Persistence

```javascript
// Check session on app load
useEffect(() => {
  supabase.auth.getSession().then(({ data: { session } }) => {
    setSession(session)
  })

  const { data: { subscription } } = supabase.auth.onAuthStateChange(
    (_event, session) => {
      setSession(session)
    }
  )

  return () => subscription.unsubscribe()
}, [])
```
