import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../lib/supabase/server'
import { z } from 'zod'
import { rateLimit } from '../../../../lib/rate-limit'
import { AdminUserProfile, UserUpdateData } from '../../../../types/admin'

// Validation schemas
const updateUserSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  role: z.enum(['admin', 'distributor', 'user', 'worker']).optional(),
  phone: z.string().optional(),
  preferred_currency: z.string().optional(),
  is_active: z.boolean().optional()
})

const banUserSchema = z.object({
  banned: z.boolean(),
  ban_reason: z.string().optional()
})

// GET /api/admin/users/[id] - Get specific user details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 20)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get admin's tenant and verify admin role
    const { data: adminProfile } = await supabase
      .from('users')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!adminProfile || adminProfile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get user details (only from same tenant)
    const { data: targetUser, error } = await supabase
      .from('users')
      .select(`
        id,
        name,
        role,
        phone,
        avatar,
        email,
        created_at,
        updated_at
      `)
      .eq('id', id)
      .eq('tenant_id', adminProfile.tenant_id) // 🔒 CRITICAL: Same tenant only
      .single()

    if (error || !targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get user's wallet balance
    const { data: wallet } = await supabase
      .from('wallets')
      .select('balance')
      .eq('user_id', id)
      .eq('tenant_id', adminProfile.tenant_id)
      .single()

    return NextResponse.json({
      user: {
        id: targetUser.id,
        email: targetUser.email,
        name: targetUser.name,
        role: targetUser.role,
        phone: targetUser.phone,
        avatar: targetUser.avatar,
        walletBalance: Number(wallet?.balance) || 0,
        createdAt: targetUser.created_at,
        updatedAt: targetUser.updated_at
      }
    })

  } catch (error) {
    console.error('Error fetching user details:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/admin/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData: UserUpdateData = updateUserSchema.parse(body)

    // Get admin's tenant and verify admin role
    const { data: adminProfile } = await supabase
      .from('users')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!adminProfile || adminProfile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Update user (only in same tenant)
    const { data: updatedUser, error } = await supabase
      .from('users')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('tenant_id', adminProfile.tenant_id) // 🔒 CRITICAL: Same tenant only
      .select()
      .single()

    if (error || !updatedUser) {
      return NextResponse.json({ error: 'User not found or update failed' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        role: updatedUser.role,
        phone: updatedUser.phone,
        updatedAt: updatedUser.updated_at
      }
    })

  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/admin/users/[id] - Ban/Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get admin's tenant and verify admin role
    const { data: adminProfile } = await supabase
      .from('users')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!adminProfile || adminProfile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Check if user exists in same tenant
    const { data: targetUser } = await supabase
      .from('users')
      .select('id, role')
      .eq('id', id)
      .eq('tenant_id', adminProfile.tenant_id) // 🔒 CRITICAL: Same tenant only
      .single()

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Prevent admin from deleting other admins
    if (targetUser.role === 'admin') {
      return NextResponse.json({ error: 'Cannot delete admin users' }, { status: 403 })
    }

    // Soft delete by updating settings (preserve data for audit)
    const { error } = await supabase
      .from('users')
      .update({
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('tenant_id', adminProfile.tenant_id)

    if (error) {
      return NextResponse.json({ error: 'Failed to ban user' }, { status: 500 })
    }

    return NextResponse.json({ success: true, message: 'User banned successfully' })

  } catch (error) {
    console.error('Error banning user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
