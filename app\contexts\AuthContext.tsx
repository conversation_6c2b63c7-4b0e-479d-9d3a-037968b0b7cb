"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { useTenant } from './TenantContext'
import type { User } from '../types'
import type { User as SupabaseUser } from '@supabase/supabase-js'

// Simplified authentication types
export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

export type AuthModalType = 'login' | 'register' | 'forgot-password' | null

export interface AuthContextType {
  // State
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
  currentModal: AuthModalType

  // Actions
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, name: string) => Promise<void>
  logout: () => Promise<void>
  resetPassword: (email: string) => Promise<void>

  // Modal controls
  openModal: (type: AuthModalType) => void
  closeModal: () => void

  // Utility functions
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { tenant } = useTenant()
  const [user, setUser] = useState<User | null>(null)
  const [currentModal, setCurrentModal] = useState<AuthModalType>(null)
  const [isLoading, setIsLoading] = useState(false) // Start optimistic - no loading flash
  const [error, setError] = useState<string | null>(null)

  // Clear error function
  const clearError = () => setError(null)

  // Helper function to create wallet if it doesn't exist
  const ensureUserWallet = async (userId: string, tenantId: string = 'default') => {
    try {
      const { data, error } = await supabase
        .from('wallets')
        .select('id')
        .eq('user_id', userId)
        .single()

      if (error && error.code === 'PGRST116') {
        // Wallet doesn't exist, create it
        console.log('Creating wallet for user:', userId)
        const { error: insertError } = await supabase
          .from('wallets')
          .insert({
            user_id: userId,
            tenant_id: tenantId,
            balance: 0
          })

        if (insertError) {
          console.error('Error creating wallet:', insertError)
        }
      }
    } catch (error) {
      console.error('Error ensuring wallet exists:', error)
    }
  }

  // Helper function to get user profile with wallet balance
  const getUserProfile = async (userId: string, tenantId?: string) => {
    try {
      console.log('Getting user profile for:', userId, 'tenant:', tenantId)

      // Ensure user has a wallet
      await ensureUserWallet(userId, tenantId || 'default')

      // Query users table with left join to wallets table
      const query = supabase
        .from('users')
        .select(`
          *,
          wallets (
            balance
          )
        `)
        .eq('id', userId)

      // Only filter by tenant if we have one
      if (tenantId) {
        query.eq('tenant_id', tenantId)
      }

      const { data, error } = await query.single()
      if (error) {
        console.error('Profile query error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          userId,
          tenantId
        })
        throw error
      }

      console.log('Profile data retrieved:', data)
      return data
    } catch (error) {
      console.error('Profile error caught:', error)
      return null
    }
  }

  // Initialize auth once - no dependencies on tenant
  useEffect(() => {
    let mounted = true

    const initAuth = async () => {
      try {
        // Get current session - this should be fast as Supabase caches it
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Session error:', error)
          return
        }

        // If we have session, load user profile
        if (session?.user && mounted) {
          const profile = await getUserProfile(session.user.id, tenant?.id)
          if (profile && mounted) {
            const userData = {
              id: profile.id,
              email: session.user.email || '',
              name: profile.name,
              role: profile.role,
              walletBalance: profile.wallets?.[0]?.balance || 0,
              avatar: profile.avatar,
              phone: profile.phone || undefined, // Handle missing phone field
              createdAt: profile.created_at
            }
            setUser(userData)
          }
        }
      } catch (error) {
        console.error('Auth init error:', error)
        if (mounted) {
          setError('فشل في تهيئة المصادقة')
        }
      }
    }

    // Run initialization immediately - no loading state needed for session check
    initAuth()

    return () => {
      mounted = false
    }
  }, []) // No dependencies - initialize once only

  // Listen for auth changes - create once, never recreate
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          const profile = await getUserProfile(session.user.id, tenant?.id)
          if (profile) {
            setUser({
              id: profile.id,
              email: session.user.email || '',
              name: profile.name,
              role: profile.role,
              walletBalance: profile.wallets?.[0]?.balance || 0,
              avatar: profile.avatar,
              phone: profile.phone || undefined, // Handle missing phone field
              createdAt: profile.created_at
            })
          }
          setIsLoading(false)
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setIsLoading(false)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, []) // No dependencies - create once only

  // Simple login function
  const login = async (email: string, password: string): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) throw error
      setCurrentModal(null)
    } catch (error) {
      setIsLoading(false)
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء تسجيل الدخول'
      setError(errorMessage)
      throw error
    }
  }

  // Simple register function
  const register = async (email: string, password: string, name: string): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            tenant_id: tenant?.id
          }
        }
      })

      if (error) throw error

      // Create user profile
      if (data.user && tenant) {
        await supabase.from('users').insert({
          id: data.user.id,
          name,
          email,
          role: 'user',
          tenant_id: tenant.id
        })
      }

      setCurrentModal(null)
    } catch (error) {
      setIsLoading(false)
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء الحساب'
      setError(errorMessage)
      throw error
    }
  }

  // Simple logout function
  const logout = async (): Promise<void> => {
    try {
      await supabase.auth.signOut()
      setUser(null)
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  // Simple reset password function
  const resetPasswordFunction = async (email: string): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      const { error } = await supabase.auth.resetPasswordForEmail(email)
      if (error) throw error
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Modal controls
  const openModal = (type: AuthModalType) => {
    clearError()
    setCurrentModal(type)
  }

  const closeModal = () => {
    clearError()
    setCurrentModal(null)
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    error,
    currentModal,
    login,
    register,
    logout,
    resetPassword: resetPasswordFunction,
    openModal,
    closeModal,
    clearError
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
