-- Add phone field to users table
-- This migration adds the missing phone field that is expected by the application code

-- Add phone column to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS phone TEXT;

-- Add comment for documentation
COMMENT ON COLUMN users.phone IS 'Optional phone number for user contact information';

-- Create index for performance on phone queries (if needed for lookups)
CREATE INDEX IF NOT EXISTS idx_users_phone 
ON users (tenant_id, phone) 
WHERE phone IS NOT NULL;

-- Update RLS policies (existing tenant_id policies will cover this new field)
-- No additional RLS policies needed as the existing tenant_id policies will cover this new field
