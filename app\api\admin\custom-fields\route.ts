import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'
import { rateLimit } from '../../../lib/rate-limit'
import { customFieldCreateSchema } from '../../../lib/products'

// GET /api/admin/custom-fields - Get custom fields for a product
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant
    const { data: profile } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'ملف المستخدم غير موجود' }, { status: 404 })
    }

    // Parse query parameters
    const url = new URL(request.url)
    const productId = url.searchParams.get('product_id')

    if (!productId) {
      return NextResponse.json({ 
        success: false,
        error: 'معرف المنتج مطلوب' 
      }, { status: 400 })
    }

    // Verify product exists and belongs to tenant
    const { data: product } = await supabase
      .from('products')
      .select('id')
      .eq('id', productId)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!product) {
      return NextResponse.json({ 
        success: false,
        error: 'المنتج غير موجود' 
      }, { status: 404 })
    }

    // Get custom fields for this product
    const { data: customFields, error } = await supabase
      .from('custom_fields')
      .select('*')
      .eq('product_id', productId)
      .eq('tenant_id', profile.tenant_id)
      .order('field_order', { ascending: true })

    if (error) {
      console.error('Database error fetching custom fields:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في جلب الحقول المخصصة',
        details: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: customFields || []
    })

  } catch (error) {
    console.error('Error in custom fields GET:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// POST /api/admin/custom-fields - Create new custom field
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    const body = await request.json()
    
    // Validate input
    const validation = customFieldCreateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        success: false,
        error: 'بيانات غير صالحة',
        details: validation.error.errors.map(e => e.message)
      }, { status: 400 })
    }

    const validatedData = validation.data

    // Verify product exists and belongs to tenant
    const { data: product } = await supabase
      .from('products')
      .select('id')
      .eq('id', validatedData.product_id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!product) {
      return NextResponse.json({ 
        success: false,
        error: 'المنتج المحدد غير موجود' 
      }, { status: 400 })
    }

    // Get the next field order if not provided
    if (validatedData.field_order === undefined) {
      const { data: lastField } = await supabase
        .from('custom_fields')
        .select('field_order')
        .eq('product_id', validatedData.product_id)
        .eq('tenant_id', profile.tenant_id)
        .order('field_order', { ascending: false })
        .limit(1)
        .single()

      validatedData.field_order = lastField ? lastField.field_order + 1 : 0
    }

    // Insert new custom field
    const { data: customField, error } = await supabase
      .from('custom_fields')
      .insert({
        ...validatedData,
        tenant_id: profile.tenant_id
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating custom field:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في إنشاء الحقل المخصص' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      data: customField 
    }, { status: 201 })

  } catch (error) {
    console.error('Error in custom fields POST:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// PUT /api/admin/custom-fields - Bulk update custom fields order
export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    const body = await request.json()
    const { fields } = body

    if (!Array.isArray(fields)) {
      return NextResponse.json({ 
        success: false,
        error: 'قائمة الحقول مطلوبة' 
      }, { status: 400 })
    }

    // Update field orders
    const updates = fields.map((field: any, index: number) => 
      supabase
        .from('custom_fields')
        .update({ field_order: index })
        .eq('id', field.id)
        .eq('tenant_id', profile.tenant_id)
    )

    const results = await Promise.all(updates)
    
    // Check for errors
    const errors = results.filter(result => result.error)
    if (errors.length > 0) {
      console.error('Error updating field orders:', errors)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في تحديث ترتيب الحقول' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: 'تم تحديث ترتيب الحقول بنجاح' 
    })

  } catch (error) {
    console.error('Error in custom fields PUT:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}
