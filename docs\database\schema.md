# Database Schema Documentation

## 🗄️ Complete Database Structure

The Bentakon system uses a PostgreSQL database with 10 core tables designed for multi-tenancy, security, and scalability.

## 📋 Table Overview

| Table | Purpose | Records | Relationships |
|-------|---------|---------|---------------|
| `users` | User profiles | User data | Extends auth.users |
| `products` | Product catalog | Product info | 1:many packages |
| `packages` | Product variants | Pricing tiers | many:1 products |
| `orders` | Order management | Purchase records | many:1 users, products |
| `digital_codes` | Code delivery | Redeemable codes | many:1 products/packages |
| `wallets` | User balances | USD balances | 1:1 users |
| `transactions` | Financial records | All money movements | many:1 users, wallets |
| `currencies` | Exchange rates | Currency data | System reference |
| `product_custom_fields` | Dynamic forms | Form definitions | many:1 products |
| `custom_field_options` | Dropdown options | Field choices | many:1 custom_fields |

## 🏗️ Detailed Table Schemas

### 1. Users Table
```sql
CREATE TABLE users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  tenant_id TEXT NOT NULL DEFAULT 'default',
  email TEXT NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'worker', 'distributor', 'user')),
  avatar TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Purpose**: Extends Supabase auth.users with profile information and role-based access control.

**Key Fields**:
- `tenant_id`: Multi-tenant isolation
- `role`: Determines system permissions
- `id`: Direct reference to auth.users

### 2. Products Table
```sql
CREATE TABLE products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id TEXT NOT NULL DEFAULT 'default',
  slug TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  image_url TEXT NOT NULL,
  category TEXT NOT NULL,
  has_packages BOOLEAN DEFAULT FALSE,
  has_codes BOOLEAN DEFAULT FALSE,
  has_custom_fields BOOLEAN DEFAULT FALSE,
  -- Simple product pricing (when has_packages = false)
  original_price NUMERIC,
  user_price NUMERIC,
  distributor_price NUMERIC,
  discount_price NUMERIC,
  currency TEXT DEFAULT 'USD',
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Purpose**: Core product catalog with flexible structure supporting multiple product types.

**Product Types**:
- **Simple Product**: Direct pricing fields, no packages
- **Package Product**: Uses packages table for variants
- **Digital Product**: Links to digital_codes table
- **Custom Product**: Uses custom_fields for user input

### 3. Packages Table
```sql
CREATE TABLE packages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id TEXT NOT NULL DEFAULT 'default',
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  image_url TEXT NOT NULL,
  original_price NUMERIC NOT NULL,
  user_price NUMERIC NOT NULL,
  distributor_price NUMERIC,
  discount_price NUMERIC,
  currency TEXT DEFAULT 'USD',
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Purpose**: Product variants with multi-tier pricing for different user roles.

**Pricing Tiers**:
- `original_price`: Cost basis for profit calculation
- `user_price`: Standard customer pricing
- `distributor_price`: Special pricing for distributors
- `discount_price`: Promotional pricing

### 4. Orders Table
```sql
CREATE TABLE orders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id TEXT NOT NULL DEFAULT 'default',
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  package_id UUID REFERENCES packages(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL, -- Customer payment
  cost DECIMAL(10,2) NOT NULL,   -- Our cost
  profit DECIMAL(10,2) NOT NULL, -- Calculated profit
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'completed')),
  pricing_tier TEXT DEFAULT 'user' CHECK (pricing_tier IN ('user', 'distributor', 'discount')),
  currency TEXT DEFAULT 'USD',
  custom_field_data JSONB, -- User input from custom fields
  note TEXT, -- Worker notes
  handled_by UUID REFERENCES auth.users(id), -- Worker who processed
  code_id UUID REFERENCES digital_codes(id), -- Assigned digital code
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Purpose**: Complete order management with workflow tracking and profit calculation.

**Order Workflow**:
1. `pending`: Created, payment deducted
2. `accepted`: Worker approved, code assigned if applicable
3. `declined`: Worker rejected, automatic refund processed
4. `completed`: Final status, order fulfilled

### 5. Digital Codes Table
```sql
CREATE TABLE digital_codes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id TEXT NOT NULL DEFAULT 'default',
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  package_id UUID REFERENCES packages(id) ON DELETE CASCADE,
  code TEXT NOT NULL, -- Store encrypted in production
  order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
  is_used BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  used_at TIMESTAMP WITH TIME ZONE,
  CONSTRAINT check_product_or_package CHECK (
    (product_id IS NOT NULL AND package_id IS NULL) OR 
    (product_id IS NULL AND package_id IS NOT NULL)
  )
);
```

**Purpose**: Manages digital code inventory and assignment to orders.

**Code Assignment**:
- Codes can belong to products OR packages (not both)
- Assigned to orders when accepted
- Marked as used when delivered to customer

### 6. Wallets Table
```sql
CREATE TABLE wallets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id TEXT NOT NULL DEFAULT 'default',
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  balance DECIMAL(10,2) DEFAULT 0 CHECK (balance >= 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Purpose**: USD-only wallet balances with non-negative constraint.

**Balance Calculation**: Always calculated from transactions table for accuracy.

### 7. Transactions Table
```sql
CREATE TABLE transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id TEXT NOT NULL DEFAULT 'default',
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID REFERENCES wallets(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('deposit', 'purchase', 'refund')),
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  currency_code TEXT DEFAULT 'USD',
  exchange_rate DECIMAL(10,6) DEFAULT 1.0,
  status TEXT DEFAULT 'confirmed' CHECK (status IN ('pending', 'confirmed', 'failed')),
  reference_id UUID, -- Order ID for purchases/refunds
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Purpose**: Immutable financial record keeping with complete audit trail.

**Transaction Types**:
- `deposit`: Money added to wallet
- `purchase`: Money deducted for orders
- `refund`: Money returned for declined orders

### 8. Currencies Table
```sql
CREATE TABLE currencies (
  code TEXT PRIMARY KEY, -- USD, EUR, SAR, etc.
  name TEXT NOT NULL,
  exchange_rate DECIMAL(10,6) NOT NULL DEFAULT 1.0,
  is_active BOOLEAN DEFAULT TRUE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Purpose**: Exchange rates for multi-currency support with USD as base.

### 9. Product Custom Fields Table
```sql
CREATE TABLE product_custom_fields (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id TEXT NOT NULL DEFAULT 'default',
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  field_type TEXT CHECK (field_type IN ('text', 'dropdown')) NOT NULL,
  label TEXT NOT NULL,
  placeholder TEXT,
  required BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Purpose**: Dynamic form fields for products requiring user input.

### 10. Custom Field Options Table
```sql
CREATE TABLE custom_field_options (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id TEXT NOT NULL DEFAULT 'default',
  custom_field_id UUID REFERENCES product_custom_fields(id) ON DELETE CASCADE,
  option_text TEXT NOT NULL,
  display_order INTEGER DEFAULT 0
);
```

**Purpose**: Dropdown options for custom fields.

## 🔗 Key Relationships

### **User-Centric Relationships**
```
auth.users (1) → users (1) [Profile Extension]
auth.users (1) → wallets (1) [Balance Management]
auth.users (1) → orders (∞) [Purchase History]
auth.users (1) → transactions (∞) [Financial History]
```

### **Product-Centric Relationships**
```
products (1) → packages (∞) [Product Variants]
products (1) → custom_fields (∞) [Dynamic Forms]
products (1) → digital_codes (∞) [Code Inventory]
custom_fields (1) → field_options (∞) [Dropdown Choices]
```

### **Order-Centric Relationships**
```
orders (∞) → products (1) [What was ordered]
orders (∞) → packages (1) [Which variant]
orders (∞) → users (1) [Who ordered]
orders (1) → digital_codes (1) [Code assignment]
orders (∞) → transactions (1) [Payment record]
```

## 📊 Indexes for Performance

```sql
-- Multi-tenant indexes
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_products_tenant_id ON products(tenant_id);
CREATE INDEX idx_orders_tenant_id ON orders(tenant_id);

-- Query optimization indexes
CREATE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_digital_codes_package_id ON digital_codes(package_id);
```

## 🔒 Security Constraints

### **Data Integrity**
- Foreign key constraints ensure referential integrity
- Check constraints validate enum values and business rules
- Unique constraints prevent duplicate critical data

### **Business Rules**
- Wallet balances cannot be negative
- Digital codes must belong to either product OR package
- Order amounts must be positive
- Transaction amounts must be positive

### **Multi-Tenant Isolation**
- Every table includes tenant_id for data separation
- RLS policies enforce tenant boundaries
- Indexes optimize tenant-scoped queries
