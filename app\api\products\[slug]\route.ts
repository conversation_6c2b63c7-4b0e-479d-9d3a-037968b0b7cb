import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'
import type { Package, CustomField } from '../../../types'

// GET /api/products/[slug] - Get single product by slug (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
): Promise<NextResponse> {
  try {
    const { slug } = await params

    // Get tenant from headers (same pattern as other API routes)
    let tenantId = request.headers.get('x-tenant-id')

    const supabase = await createClient()

    // If no tenant in headers, try to get from user profile or use fallback
    if (!tenantId) {
      try {
        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('tenant_id')
            .eq('id', user.id)
            .single()

          tenantId = profile?.tenant_id
        }
      } catch (authError) {
        // No authenticated user, will use main tenant fallback
      }
    }

    // Fallback to main tenant if no tenant found
    if (!tenantId) {
      const { data: mainTenant } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', 'main')
        .single()

      tenantId = mainTenant?.id || process.env.DEFAULT_TENANT_ID || 'default-tenant'
    }



    // Get product with related data (public data only)
    const { data: product, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        slug,
        description,
        category,
        image_url,
        original_price,
        user_price,
        distributor_price,
        discount_price,
        has_packages,
        has_codes,
        has_custom_fields,
        created_at
      `)
      .eq('slug', slug)
      .eq('tenant_id', tenantId)
      .single()



    if (error || !product) {
      return NextResponse.json({
        success: false,
        error: 'المنتج غير موجود'
      }, { status: 404 })
    }

    // Get packages if product has packages
    let packages: Package[] = []
    if (product.has_packages) {
      const { data: packagesData } = await supabase
        .from('packages')
        .select(`
          id,
          tenant_id,
          product_id,
          name,
          description,
          image_url,
          original_price,
          user_price,
          distributor_price,
          discount_price,
          digital_codes,
          created_at,
          updated_at
        `)
        .eq('product_id', product.id)
        .order('created_at', { ascending: true })
      packages = (packagesData as Package[]) || []
    }

    // Get custom fields if product has custom fields
    let customFields: CustomField[] = []
    if (product.has_custom_fields) {
      const { data: customFieldsData } = await supabase
        .from('product_custom_fields')
        .select(`
          id,
          tenant_id,
          product_id,
          label,
          field_type,
          field_order,
          required,
          placeholder,
          validation_rules,
          created_at,
          updated_at
        `)
        .eq('product_id', product.id)
        .order('created_at', { ascending: true })
      customFields = (customFieldsData as CustomField[]) || []
    }

    // Combine all data
    const productWithDetails = {
      ...product,
      packages,
      custom_fields: customFields
    }

    return NextResponse.json({
      success: true,
      data: productWithDetails
    })

  } catch (error) {
    console.error('Error in product GET:', error)
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ غير متوقع'
    }, { status: 500 })
  }
}
