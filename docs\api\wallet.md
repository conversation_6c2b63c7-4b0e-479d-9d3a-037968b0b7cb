# Wallet API Documentation

## 💰 Overview

The wallet system manages user balances in USD with multi-currency deposit support. All financial operations are handled server-side with complete audit trails and automatic balance calculations.

## 🏗️ Wallet Architecture

```
User Registration → Wallet Creation → Deposits → Purchases → Refunds
                                   ↓
                            Transaction History → Balance Calculation
```

### Key Principles

1. **USD-Only Storage**: All balances stored in USD for consistency
2. **Multi-Currency Deposits**: Support deposits in various currencies with real-time conversion
3. **Immutable Transactions**: All financial operations create permanent transaction records
4. **Automatic Balance Calculation**: Balance calculated from transaction history
5. **Non-Negative Balances**: Wallet balances cannot go below zero

## 📡 API Endpoints

### 1. Get Wallet Information

**Endpoint**: `GET /api/wallet`

**Purpose**: Retrieves user's wallet balance and recent transaction history.

**Headers**:
```
Authorization: Bearer jwt-token-here
```

**Response Success** (200):
```json
{
  "success": true,
  "data": {
    "wallet": {
      "balance": 150.75,
      "currency": "USD",
      "updated_at": "2024-01-15T10:30:00Z"
    },
    "transactions": [
      {
        "id": "txn-uuid-1",
        "type": "deposit",
        "amount": 100.00,
        "currency_code": "EUR",
        "exchange_rate": 0.85,
        "description": "Deposit via credit card",
        "status": "confirmed",
        "created_at": "2024-01-15T09:00:00Z"
      },
      {
        "id": "txn-uuid-2",
        "type": "purchase",
        "amount": 25.50,
        "currency_code": "USD",
        "exchange_rate": 1.0,
        "description": "Purchase: Gaming Package",
        "status": "confirmed",
        "created_at": "2024-01-15T10:15:00Z"
      }
    ]
  }
}
```

**Response Error** (401):
```json
{
  "success": false,
  "error": "غير مصرح"
}
```

**Implementation**:
```javascript
export async function GET(request: NextRequest) {
  const supabase = await createSupabaseServerClient()
  
  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    return NextResponse.json({
      success: false,
      error: 'غير مصرح'
    }, { status: 401 })
  }

  // Get wallet balance
  const { data: wallet } = await supabase
    .from('wallets')
    .select('id, balance, updated_at')
    .eq('user_id', user.id)
    .eq('tenant_id', getCurrentTenant())
    .single()

  // Get recent transactions
  const { data: transactions } = await supabase
    .from('transactions')
    .select('id, type, amount, currency_code, exchange_rate, description, created_at, status')
    .eq('user_id', user.id)
    .eq('tenant_id', getCurrentTenant())
    .order('created_at', { ascending: false })
    .limit(10)

  return NextResponse.json({
    success: true,
    data: {
      wallet: {
        balance: wallet.balance,
        currency: 'USD',
        updated_at: wallet.updated_at
      },
      transactions: transactions || []
    }
  })
}
```

### 2. Process Deposit

**Endpoint**: `POST /api/wallet/deposit`

**Purpose**: Adds money to user's wallet with currency conversion support.

**Headers**:
```
Authorization: Bearer jwt-token-here
Content-Type: application/json
```

**Request Body**:
```json
{
  "amount": 100.00,
  "currency": "EUR",
  "description": "Credit card deposit"
}
```

**Validation Rules**:
- `amount`: Must be positive number
- `currency`: Must be supported currency code (USD, EUR, SAR, GBP)
- `description`: Optional, defaults to "إيداع في المحفظة"

**Response Success** (200):
```json
{
  "success": true,
  "data": {
    "transaction_id": "txn-uuid-here",
    "new_balance": 235.75,
    "amount_deposited": 100.00,
    "currency": "EUR",
    "exchange_rate": 0.85
  },
  "message": "تم إيداع المبلغ بنجاح"
}
```

**Response Error** (400):
```json
{
  "success": false,
  "error": "عملة غير مدعومة",
  "details": [
    {
      "code": "invalid_currency",
      "message": "Currency not supported"
    }
  ]
}
```

**Backend Process**:
1. Validate input data
2. Get current exchange rate from currencies table
3. Convert amount to USD
4. Call `process_deposit()` database function
5. Update wallet balance
6. Return transaction details

**Implementation**:
```javascript
export async function POST(request: NextRequest) {
  const body = await request.json()
  
  // Validate input
  const validationResult = depositSchema.safeParse(body)
  if (!validationResult.success) {
    return NextResponse.json({
      success: false,
      error: 'بيانات غير صالحة',
      details: validationResult.error.errors
    }, { status: 400 })
  }

  const { amount, currency, description } = validationResult.data
  const supabase = await createSupabaseServerClient()
  
  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    return NextResponse.json({
      success: false,
      error: 'غير مصرح'
    }, { status: 401 })
  }

  // Get exchange rate
  let exchangeRate = 1.0
  if (currency !== 'USD') {
    const { data: currencyData, error: currencyError } = await supabase
      .from('currencies')
      .select('exchange_rate')
      .eq('code', currency)
      .eq('is_active', true)
      .single()

    if (currencyError || !currencyData) {
      return NextResponse.json({
        success: false,
        error: 'عملة غير مدعومة'
      }, { status: 400 })
    }
    
    exchangeRate = currencyData.exchange_rate
  }

  // Process deposit
  const { data: transactionId, error: depositError } = await supabase
    .rpc('process_deposit', {
      user_uuid: user.id,
      amount_val: amount,
      currency_code_val: currency,
      exchange_rate_val: exchangeRate,
      description_val: description
    })

  if (depositError) {
    return NextResponse.json({
      success: false,
      error: 'فشل في معالجة الإيداع'
    }, { status: 500 })
  }

  // Get updated balance
  const { data: wallet } = await supabase
    .from('wallets')
    .select('balance')
    .eq('user_id', user.id)
    .eq('tenant_id', getCurrentTenant())
    .single()

  return NextResponse.json({
    success: true,
    data: {
      transaction_id: transactionId,
      new_balance: wallet?.balance || 0,
      amount_deposited: amount,
      currency: currency,
      exchange_rate: exchangeRate
    },
    message: 'تم إيداع المبلغ بنجاح'
  })
}
```

### 3. Get Transaction History

**Endpoint**: `GET /api/wallet/transactions`

**Purpose**: Retrieves paginated transaction history with filtering options.

**Headers**:
```
Authorization: Bearer jwt-token-here
```

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `type`: Filter by transaction type (deposit, purchase, refund)

**Example Request**:
```
GET /api/wallet/transactions?page=1&limit=10&type=deposit
```

**Response Success** (200):
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": "txn-uuid-1",
        "type": "deposit",
        "amount": 100.00,
        "currency_code": "EUR",
        "exchange_rate": 0.85,
        "description": "Credit card deposit",
        "status": "confirmed",
        "reference_id": null,
        "created_at": "2024-01-15T09:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

**Implementation**:
```javascript
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
  const type = searchParams.get('type')
  
  const supabase = await createSupabaseServerClient()
  
  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    return NextResponse.json({
      success: false,
      error: 'غير مصرح'
    }, { status: 401 })
  }

  // Build query
  let query = supabase
    .from('transactions')
    .select(`
      id, type, amount, currency_code, exchange_rate,
      description, status, reference_id, created_at
    `)
    .eq('user_id', user.id)
    .eq('tenant_id', getCurrentTenant())
    .order('created_at', { ascending: false })

  // Add type filter
  if (type && ['deposit', 'purchase', 'refund'].includes(type)) {
    query = query.eq('type', type)
  }

  // Add pagination
  const from = (page - 1) * limit
  const to = from + limit - 1
  query = query.range(from, to)

  const { data: transactions, error: transactionsError } = await query

  if (transactionsError) {
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب المعاملات'
    }, { status: 500 })
  }

  // Get total count
  const { count: totalCount } = await supabase
    .from('transactions')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)
    .eq('tenant_id', getCurrentTenant())

  return NextResponse.json({
    success: true,
    data: {
      transactions: transactions || [],
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        pages: Math.ceil((totalCount || 0) / limit)
      }
    }
  })
}
```

## 💱 Currency Conversion

### Supported Currencies

| Code | Name | Example Rate |
|------|------|--------------|
| USD | US Dollar | 1.0 (base) |
| EUR | Euro | 0.85 |
| SAR | Saudi Riyal | 3.75 |
| GBP | British Pound | 0.75 |

### Exchange Rate Management

Exchange rates are stored in the `currencies` table and updated by admins:

```sql
-- Update exchange rates
UPDATE currencies 
SET exchange_rate = 0.87, updated_at = NOW() 
WHERE code = 'EUR';
```

### Conversion Logic

```javascript
// Convert foreign currency to USD
const usdAmount = foreignAmount * exchangeRate

// Example: 100 EUR * 0.85 = 85 USD
```

## 🔒 Security Features

### 1. Balance Validation

```javascript
// Prevent negative balances
CHECK (balance >= 0)

// Validate positive transaction amounts
CHECK (amount > 0)
```

### 2. Atomic Operations

All wallet operations use database functions to ensure atomicity:

```javascript
// Atomic deposit processing
const { data: transactionId } = await supabase
  .rpc('process_deposit', {
    user_uuid: user.id,
    amount_val: amount,
    currency_code_val: currency,
    exchange_rate_val: exchangeRate,
    description_val: description
  })
```

### 3. Audit Trail

Every financial operation creates a permanent transaction record:

```sql
-- Transaction audit trail
SELECT 
  type,
  amount,
  currency_code,
  exchange_rate,
  description,
  created_at
FROM transactions 
WHERE user_id = 'user-uuid'
ORDER BY created_at DESC;
```

## 🧪 Testing Wallet Operations

### Test Deposit

```javascript
// Test deposit
const response = await fetch('/api/wallet/deposit', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    amount: 100.00,
    currency: 'EUR',
    description: 'Test deposit'
  })
})

const result = await response.json()
console.log('Deposit result:', result)
```

### Test Balance Calculation

```javascript
// Verify balance calculation
const response = await fetch('/api/wallet', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})

const result = await response.json()
console.log('Current balance:', result.data.wallet.balance)
```

## 🔧 Error Handling

### Common Errors

| Error | Code | Description |
|-------|------|-------------|
| Insufficient Balance | `insufficient_balance` | Not enough funds for purchase |
| Invalid Currency | `invalid_currency` | Currency not supported |
| Negative Amount | `negative_amount` | Amount must be positive |
| Wallet Not Found | `wallet_not_found` | User wallet doesn't exist |

### Error Response Format

```json
{
  "success": false,
  "error": "رصيد المحفظة غير كافي",
  "code": "insufficient_balance",
  "details": {
    "current_balance": 25.50,
    "required_amount": 50.00
  }
}
```

## 📊 Wallet Analytics

### Balance History

```sql
-- Track balance changes over time
SELECT 
  DATE(created_at) as date,
  SUM(CASE WHEN type IN ('deposit', 'refund') THEN amount ELSE -amount END) as daily_change
FROM transactions 
WHERE user_id = 'user-uuid'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

### Transaction Summary

```sql
-- Monthly transaction summary
SELECT 
  DATE_TRUNC('month', created_at) as month,
  type,
  COUNT(*) as transaction_count,
  SUM(amount) as total_amount
FROM transactions 
WHERE user_id = 'user-uuid'
GROUP BY month, type
ORDER BY month DESC, type;
```
