# Bentakon E-Commerce System Documentation

## 🏗️ System Overview

Bentakon is a modern, multi-tenant e-commerce platform built with Next.js and Supabase, designed for digital product sales with advanced features like multi-tier pricing, wallet systems, and role-based access control.

### 🎯 Key Features

- **Multi-tenant Architecture** - Complete data isolation between tenants
- **Role-based Access Control** - Admin, Worker, Distributor, and User roles
- **Advanced Product System** - Simple products, packages, digital codes, custom fields
- **Wallet System** - USD-based with multi-currency support and automatic conversions
- **Order Workflow** - Pending → Accepted/Declined → Completed with automatic refunds
- **Multi-tier Pricing** - User, Distributor, and Discount pricing levels
- **Real-time Operations** - Instant wallet deductions and balance updates

## 🛠️ Technology Stack

### **Frontend**
- **Next.js 14** - App Router with Server/Client Components
- **TypeScript** - Full type safety
- **Tailwind CSS** - Utility-first styling
- **Shadcn/UI** - Modern component library
- **SWR** - Data fetching and caching

### **Backend**
- **Next.js API Routes** - Server-side business logic
- **Supabase** - PostgreSQL database with real-time features
- **Row Level Security (RLS)** - Database-level security
- **Zod** - Runtime type validation

### **Database**
- **PostgreSQL** - Primary database
- **Supabase Functions** - Server-side database operations
- **Real-time Subscriptions** - Live data updates

## 🏛️ Architecture Principles

### **1. Server-Side Security First**
- All business logic runs on the server
- Client-side components are display-only
- No sensitive calculations in the browser
- Database functions handle critical operations

### **2. Multi-Tenant Isolation**
- Every table includes `tenant_id` for data separation
- RLS policies enforce tenant boundaries
- API routes automatically filter by tenant
- Complete data isolation between customers

### **3. Role-Based Access Control**
```
Admin     → Full system access, user management, product creation
Worker    → Order processing, status updates, code assignment
Distributor → Special pricing tier, bulk operations
User      → Product browsing, purchasing, wallet management
```

### **4. Immutable Financial Operations**
- Wallet transactions are append-only
- Balance calculated from transaction history
- Automatic refunds on order declines
- Complete audit trail for all financial operations

## 📊 Database Structure Summary

### **Core Tables (10 Total)**
1. **`users`** - User profiles extending Supabase auth
2. **`products`** - Product catalog with type flags
3. **`packages`** - Product variants with multi-tier pricing
4. **`orders`** - Order management with profit tracking
5. **`digital_codes`** - Code delivery system
6. **`wallets`** - USD-only wallet balances
7. **`transactions`** - All financial operations
8. **`currencies`** - Exchange rates for conversion
9. **`product_custom_fields`** - Dynamic form fields
10. **`custom_field_options`** - Dropdown field options

### **Key Relationships**
```
auth.users → users (profile extension)
users → wallets (1:1 relationship)
users → orders (1:many)
users → transactions (1:many)
products → packages (1:many)
products → custom_fields (1:many)
orders → digital_codes (1:1 when applicable)
```

## 🔄 Data Flow Architecture

### **Request Flow**
```
Client Request → Next.js API Route → Supabase Client → Database → RLS Check → Response
```

### **Authentication Flow**
```
Login → Supabase Auth → JWT Token → API Middleware → Role Check → Access Granted
```

### **Order Flow**
```
Product Selection → Pricing Calculation → Wallet Check → Order Creation → 
Payment Deduction → Worker Processing → Code Assignment → Completion
```

## 🚀 Quick Start

1. **Environment Setup**
   ```bash
   cp .env.example .env.local
   # Configure Supabase credentials
   ```

2. **Database Setup**
   ```bash
   # Run database schema
   psql -f lib/database-schema.sql
   ```

3. **Development**
   ```bash
   npm install
   npm run dev
   ```

## 📚 Documentation Structure

- **[Database](./database/)** - Schema, migrations, functions, RLS policies
- **[API](./api/)** - All backend endpoints and their usage
- **[Frontend](./frontend/)** - Components, pages, and UI patterns
- **[Business Logic](./business/)** - Pricing, orders, wallet operations
- **[Workflows](./workflows/)** - User journeys and admin processes
- **[Development](./development/)** - Setup, standards, testing, deployment
- **[Roles & Permissions](./roles.md)** - Complete role-based access matrix

## ⚠️ Critical Security Notes

1. **Never expose service keys** to the client
2. **Always validate user roles** in API routes
3. **Use database functions** for financial operations
4. **Enforce tenant isolation** in all queries
5. **Validate all inputs** with Zod schemas

## 🎯 System Goals

- **Security**: Multi-layered security with RLS and role-based access
- **Scalability**: Multi-tenant architecture supporting unlimited customers
- **Maintainability**: Clean separation of concerns and comprehensive documentation
- **Performance**: Efficient queries with proper indexing and caching
- **User Experience**: Intuitive interfaces for all user types

---

**Next Steps**: Read the specific documentation sections for detailed implementation guides and API references.
