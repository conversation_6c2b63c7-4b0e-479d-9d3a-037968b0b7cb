import { NextRequest, NextResponse } from 'next/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'
import { z } from 'zod'
import { getUserFromHeaders, requireAdmin, unauthorizedResponse, forbiddenResponse, getTenantId } from '../../../lib/auth-helpers'

// Simple validation schema
const createUserSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1).max(100),
  role: z.enum(['admin', 'distributor', 'user', 'worker']),
  phone: z.string().optional(),
  password: z.string().min(6).max(100)
})

const updateUserSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  role: z.enum(['admin', 'distributor', 'user', 'worker']).optional(),
  phone: z.string().optional(),
  is_active: z.boolean().optional()
})

export async function GET(request: NextRequest): Promise<NextResponse> {
  // Check authentication (set by middleware)
  const user = getUserFromHeaders(request)
  if (!requireAdmin(user)) {
    return user ? forbiddenResponse('Admin access required') : unauthorizedResponse()
  }

  try {
    const tenantId = getTenantId(request)

    // Create service client for admin operations
    const serviceClient = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    const { data: users, error } = await serviceClient
      .from('users')
      .select(`
        id,
        name,
        role,
        phone,
        avatar,
        email,
        created_at,
        updated_at
      `)
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    // Get currency balances for all users
    const userIds = users?.map(u => u.id) || []
    const { data: balances } = await serviceClient
      .from('user_currency_balances')
      .select(`
        user_id,
        currency_code,
        balance,
        currencies (
          name,
          exchange_rate
        )
      `)
      .in('user_id', userIds)

    // Combine user data with balances
    const usersWithBalances = users?.map(user => ({
      id: user.id,
      email: user.email || '',
      name: user.name,
      role: user.role,
      phone: user.phone,
      avatar: user.avatar,
      preferredCurrency: user.preferred_currency,
      walletBalance: Number(user.wallet_balance) || 0,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      currencyBalances: balances
        ?.filter(b => b.user_id === user.id)
        ?.map(b => ({
          currencyCode: b.currency_code,
          balance: Number(b.balance) || 0,
          currencyName: (b.currencies as any)?.name || b.currency_code,
          exchangeRate: Number((b.currencies as any)?.exchange_rate) || 1
        })) || []
    })) || []

    return NextResponse.json({ users: usersWithBalances })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  // Check authentication (set by middleware)
  const user = getUserFromHeaders(request)
  if (!requireAdmin(user)) {
    return user ? forbiddenResponse('Admin access required') : unauthorizedResponse()
  }

  try {
    const tenantId = getTenantId(request)
    const body = await request.json()
    const validatedData = createUserSchema.parse(body)

    // Create service client for admin operations
    const serviceClient = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Check if user already exists in this tenant
    const { data: existingProfile } = await serviceClient
      .from('users')
      .select('id')
      .eq('email', validatedData.email)
      .eq('tenant_id', tenantId)
      .single()

    if (existingProfile) {
      return NextResponse.json({ error: 'User already exists in this tenant' }, { status: 409 })
    }

    // Create user in Supabase Auth
    const { data: authData, error: authError } = await serviceClient.auth.admin.createUser({
      email: validatedData.email,
      password: validatedData.password,
      user_metadata: {
        name: validatedData.name,
        tenant_id: tenantId
      }
    })

    if (authError) {
      return NextResponse.json({ error: authError.message }, { status: 400 })
    }

    // Create user profile
    const { data: newUser, error: profileError } = await serviceClient
      .from('users')
      .insert({
        id: authData.user.id,
        name: validatedData.name,
        role: validatedData.role,
        phone: validatedData.phone,
        tenant_id: tenantId,
        email: validatedData.email
      })
      .select()
      .single()

    if (profileError) throw profileError

    return NextResponse.json({
      user: {
        id: newUser.id,
        email: validatedData.email,
        name: newUser.name,
        role: newUser.role,
        phone: newUser.phone,
        createdAt: newUser.created_at
      }
    })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
