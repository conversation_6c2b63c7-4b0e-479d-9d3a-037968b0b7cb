"use client"

import { useState, useEffect } from 'react'
import { Upload, X } from 'lucide-react'
import { generateSlug } from '../../../lib/products'


interface BasicInfoTabProps {
  formData: {
    name: string
    slug: string
    description: string
    category: string
    image_url: string
    active: boolean
  }
  onChange: (data: Partial<BasicInfoTabProps['formData']>) => void
  errors: Record<string, string>
  loading?: boolean
}

export default function BasicInfoTab({
  formData,
  onChange,
  errors,
  loading = false
}: BasicInfoTabProps) {


  // Auto-generate slug when name changes
  useEffect(() => {
    if (formData.name && !formData.slug) {
      onChange({ slug: generateSlug(formData.name) })
    }
  }, [formData.name, formData.slug, onChange])

  // Tags functionality removed as it's not part of the current database schema

  return (
    <div className="space-y-6">
      {/* Name and Slug Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-200">
            اسم المنتج <span className="text-red-400">*</span>
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => onChange({ name: e.target.value })}
            className={`w-full px-4 py-3 rounded-lg border ${
              errors.name
                ? 'border-red-500 focus:border-red-500'
                : 'border-gray-600 focus:border-purple-500'
            } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
            placeholder="أدخل اسم المنتج..."
            disabled={loading}
          />
          {errors.name && (
            <p className="text-red-400 text-sm mt-1">{errors.name}</p>
          )}
        </div>

        {/* Slug */}
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-200">
            الرابط المختصر <span className="text-red-400">*</span>
          </label>
          <input
            type="text"
            value={formData.slug}
            onChange={(e) => onChange({ slug: e.target.value })}
            className={`w-full px-4 py-3 rounded-lg border ${
              errors.slug
                ? 'border-red-500 focus:border-red-500'
                : 'border-gray-600 focus:border-purple-500'
            } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
            placeholder="product-slug"
            disabled={loading}
          />
          {errors.slug && (
            <p className="text-red-400 text-sm mt-1">{errors.slug}</p>
          )}
          <p className="text-gray-400 text-xs mt-1">
            يجب أن يحتوي على أحرف صغيرة وأرقام وشرطات فقط
          </p>
        </div>
      </div>

      {/* Category */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-200">
          الفئة <span className="text-red-400">*</span>
        </label>
        <input
          type="text"
          value={formData.category}
          onChange={(e) => onChange({ category: e.target.value })}
          className={`w-full px-4 py-3 rounded-lg border ${
            errors.category
              ? 'border-red-500 focus:border-red-500'
              : 'border-gray-600 focus:border-purple-500'
          } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
          placeholder="أدخل فئة المنتج..."
          disabled={loading}
        />
        {errors.category && (
          <p className="text-red-400 text-sm mt-1">{errors.category}</p>
        )}
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-200">
          وصف المنتج <span className="text-red-400">*</span>
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => onChange({ description: e.target.value })}
          rows={4}
          className={`w-full px-4 py-3 rounded-lg border ${
            errors.description
              ? 'border-red-500 focus:border-red-500'
              : 'border-gray-600 focus:border-purple-500'
          } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all resize-none`}
          placeholder="أدخل وصف مفصل للمنتج..."
          disabled={loading}
        />
        {errors.description && (
          <p className="text-red-400 text-sm mt-1">{errors.description}</p>
        )}
      </div>

      {/* Cover Image */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-200">
          صورة الغلاف
        </label>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="space-y-3">
            <input
              type="url"
              value={formData.image_url}
              onChange={(e) => onChange({ image_url: e.target.value })}
              className={`w-full px-4 py-3 rounded-lg border ${
                errors.image_url
                  ? 'border-red-500 focus:border-red-500'
                  : 'border-gray-600 focus:border-purple-500'
              } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
              placeholder="https://example.com/image.jpg"
              disabled={loading}
            />
            {errors.image_url && (
              <p className="text-red-400 text-sm">{errors.image_url}</p>
            )}
            <p className="text-gray-400 text-xs">
              أدخل رابط صورة صالح أو اتركه فارغاً
            </p>
          </div>
          {formData.image_url && (
            <div className="relative w-full h-48 rounded-lg overflow-hidden bg-gray-800 border border-gray-700">
              <img
                src={formData.image_url}
                alt="معاينة الصورة"
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                }}
              />
            </div>
          )}
        </div>
      </div>


    </div>
  )
}
